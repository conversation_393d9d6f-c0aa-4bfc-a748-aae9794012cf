{"images": "Image", "videos": "Video", "audio": "Audio", "file": "File", "download": "Download", "oopsLostInSpace": "Expired", "uploadImage": "Upload Image", "videoLength": "Pick Video Length", "videoLength14": "14 Frames", "videoLength25": "25 Frames", "videoSizingStrategy": "Pick video sizing strategy", "videoSizingStrategyAspectRatio": "Maintain aspect ratio", "videoSizingStrategyCrop169": "Crop to 16/9", "videoSizingStrategyImageDimensions": "Use image dimensions", "anError": "An error occurred", "empty": "Empty", "generateVideoEM": "✨ Generate Video", "pleaseSelectImage": "Please select an image", "uploadVideo": "Upload Video", "resolution": "Resolution", "pickStyle": "Pick a style", "pickUpscale": "Pick an upscale model", "photoRealisticX4Plus": "Photo realistic X4 plus", "animationX4Plus": "Animation X4 plus", "realisticAnimation": "Realistic animation", "videoUpscale": "Video Upscale", "pleaseSelectVideo": "Please select a video", "fps": "Frames per second", "horizontalResolution": "Horizontal Resolution", "uploadAudioClip": "Upload Audio Clip", "audioClip": "Audio Clip", "subjectImage": "Subject Image", "pickSubjectImage": "Pick Subject Image", "preProcess": "Pre process", "crop": "Crop", "resize": "Resize", "full": "Full", "faceRenderer": "<PERSON>", "defaultT": "<PERSON><PERSON><PERSON>", "advanced": "Advanced", "faceModelResolution": "Face model resolution", "poseStyle": "Pose Style", "expressionScale": "Expression Scale", "enhanceFace": "<PERSON><PERSON><PERSON>", "isStill": "Is still", "eyeBlink": "Eye blink", "nickname": "Nickname", "update": "Update", "promptColon": "Prompt: ", "seedArgs": "Seed: {}", "modelArgs": "Model: {}", "toExtendMusicUploadReferenceMusic": "To extend music, upload reference music", "promptMaker": "Prompt Maker", "textToMusic": "Text to Music", "musicRemixer": "Music Remixer", "instrument": "Instrument", "piano": "Piano", "violin": "Violin", "acousticGuitar": "Acoustic Guitar", "electricGuitar": "Electric Guitar", "drums": "Drums", "bassGuitar": "Bass Guitar", "synthesizer": "Synthesizer", "saxophone": "Saxophone", "flute": "Flute", "clarinet": "Clarinet", "trumpet": "Trumpet", "cello": "Cello", "viola": "<PERSON>", "trombone": "Trom<PERSON>", "harp": "Harp", "accordion": "Accordion", "oboe": "Oboe", "frenchHorn": "French Horn", "ukulele": "<PERSON><PERSON><PERSON><PERSON>", "harmonica": "Harmonica", "mandolin": "Mandolin", "banjo": "<PERSON><PERSON>", "doubleBass": "Double Bass", "bongoDrums": "Bongo Drums", "congaDrums": "Conga Drums", "timpani": "<PERSON><PERSON><PERSON>", "xylophone": "Xylophone", "marimba": "Marimba", "tambourine": "<PERSON><PERSON><PERSON><PERSON>", "djembe": "Djembe", "search": "Search", "uploadAudio": "Upload Audio", "music": "Music", "uploadMusic": "Upload Music", "toUploadAudioStorage": "To play songs and provide the best experience, VibeTunes app requires access to your Storage.", "toUploadAudioMusicNAudio": "To play songs and provide the best experience, VibeTunes app requires access to your Music and audio.", "pop": "Pop", "rock": "Rock", "classical": "Classical", "jazz": "Jazz", "blues": "Blues", "hipHop": "Hip-Hop", "electronicEdm": "Electronic (EDM)", "country": "Country", "reggae": "Reggae", "folk": "Folk", "rnbRhythmAndBlues": "R&B (Rhythm and Blues)", "loFi": "Lo-fi", "metal": "Metal", "soul": "Soul", "funk": "<PERSON>", "gospel": "Gospel", "disco": "Disco", "house": "House", "techno": "Techno", "trance": "Trance", "dubstep": "Dubstep", "drumAndBass": "Drum and Bass", "ambient": "Ambient", "indie": "Indie", "grunge": "Grunge", "punk": "Punk", "ska": "Ska", "worldMusic": "World Music", "newAge": "New Age", "latin": "Latin", "progressiveRock": "Progressive Rock", "hardRock": "Hard Rock", "heavyMetal": "Heavy Metal", "psychedelicRock": "Psychedelic Rock", "folkRock": "Folk Rock", "bluesRock": "Blues Rock", "reggaeton": "Reggaeton", "salsa": "Salsa", "bossaNova": "Bossa Nova", "flamenco": "Flamenco", "afrobeat": "Afrobeat", "dancehall": "Dancehall", "kPop": "K-pop", "jPop": "J-pop", "cPop": "C-pop", "samba": "Samba", "merengue": "<PERSON><PERSON><PERSON>", "bachata": "Bachata", "calypso": "Calypso", "neoSoul": "Neo-Soul", "output": "Output", "duration": "Duration", "referenceMusic": "Reference Music", "uploadReferenceMusic": "Upload Reference Music", "audioFileInfluenceGeneratedMusic": "An audio file that will influence the generated music", "continuation": "Continuation", "ifContinuationEnabled": "If continuation is enabled, the generated music will be a continuation of the uploaded audio. Otherwise, the generated music will mimic the audio's melody.", "startTimeAudioContinuation": "Start time of the audio file to use for continuation.", "continuationStart": "Continuation Start", "endTimeAudioContinuation": "End time of the audio file to use for continuation.", "continuationEnd": "Continuation End", "increasesInfluenceOfInputsOnOutput": "Increases the influence of inputs on the output. Higher values produce lower-variance outputs that adhere more closely to inputs.", "outputFormat": "Output Format", "generateMusic": "✨ Generate Music", "musicComposer": "Music Composer", "pickSong": "Pick a song", "searchSong": "Search a song", "pickVoice": "Pick a voice", "extendSong": "Extend a song", "style": "Style", "guidance": "Guidance", "seed": "Seed", "seedDesc": "(0=random, max: 2147483647)", "seedDescArgs": "(0=random, max: {})", "promptStar": "Prompt*", "textToAudio": "Text to Audio", "voiceToVoice": "Voice to Voice", "audioCover": "Audio Cover", "transcriptST": "Transcript*", "transcript": "Transcript", "transcriptOfAudio": "Transcript of the audio", "uploadSpeakersVoice": "Upload speaker's voice", "emotion": "Emotion", "uploadTranscriptAudio": "Upload transcript audio", "anAudioYouWantToUseItsTranscript": "An audio you want to use its transcript", "uploadTargetAudio": "Upload target audio", "outputSampleVoiceToSpeakTheTranscript": "Output sample voice to speak the transcript", "uploadInitialAudio": "Upload initial audio", "language": "Language", "gender": "Gender", "all": "All", "maleGender": "Male", "femaleGender": "Female", "neutral": "Neutral", "guidanceScale": "Guidance Scale", "steps": "Steps", "frames": "Frames", "d3": "3D", "realistic": "Realistic", "illustrated": "Illustrated", "animation": "Animation", "generateEm": "✨ Generate", "generateCartoonify": "✨ Cartoonify", "generate": "Generate", "generateS": "✨ Generate", "generateImages": "✨ Generate Images", "generateImage": "✨ Generate Image", "templates": "Templates", "imageHeight": "Image Height", "imageWidth": "Image Width", "samples": "<PERSON><PERSON>", "negativePromptOptional": "Negative Prompt (optional)", "negativePromptDesc": "Items you don't want to see in the image", "negativePromptVideoDesc": "Items you don't want to see in the video", "keepOriginalAudio": "Keep Original Audio", "targetVideo": "Target Video", "sourceVideo": "Source Video", "uploadFaceImage": "Upload Face Image", "enhancedArt": "Enhanced Art", "cinematic": "Cinematic", "comicBook": "Comic Book", "craftClay": "Craft Clay", "digitalArt": "Digital Art", "analogFilm": "Analog Film", "fantasyArt": "Fantasy Art", "isometric": "Isometric", "lineArt": "Line Art", "lowPoly": "Low Poly", "cyberPunk": "Cyber Punk", "neonPunk": "Neon Punk", "origami": "Origami", "photographic": "Photographic", "pixelArt": "Pixel Art", "texture": "Texture", "watercolor": "Watercolor", "model3d": "3D Model", "exampleActual8KPortrait": "Example: actual 8K portrait photo of gareth person, portrait, happy colors, bright eyes, clear eyes, warm smile, smooth soft skin, big dreamy eyes...", "textToVideoHintText": "An astronaut riding a horse", "styleImage": "Theme Style Image", "uploadStyleImage": "Upload Theme Style Image", "uploadImages": "Upload Images", "selectNImages": "Select {} images:", "videoHeight": "Video Height", "videoWidth": "Video Width", "promptStart": "Prompt Start*", "promptEnd": "Prompt End*", "orBig": "OR", "forgotPasswordQ": "Forgot Password?", "forgotPass": "Forgot Password", "password": "Password", "selectImageRatio": "Select Image Ratio", "selectVideoRatio": "Select Video Ratio", "imageRatio": "Image Ratio", "square": "Square", "landscape": "Landscape", "socialMedia": "Social Media", "portrait": "Portrait", "standard": "Standard", "copiedSuccessfully": "Copied Successfully", "somethingWentWrongPleaseTryLater": "Something went wrong, please try later", "somethingWentWrongPleaseCheckYourNetwork": "Something went wrong\n please check your network", "processingDot": "processing...", "more": "More", "less": "Less", "none": "None", "photoRealistic": "Photo Realistic", "pleaseSignInToProceed": "Please Sign In to Proceed", "invalidOTP": "Invalid OTP", "lowBalance": "Low Balance", "pleaseTopUp": "Please top up to continue your request.", "delete": "Delete", "areYouSure": "Are you sure?", "close": "Close", "deletedSuccessfully": "Deleted Successfully", "error": "Error", "signIn": "Sign In", "pleaseEnterValidEmail": "Please enter a valid email", "fillAllFields": "Fill all fields", "signUp": "Sign Up", "pleaseFillRequiredFields": "Please fill required fields", "bySigningUpAgreeToThe ": "By signing up, you agree to the ", "termsOfUse": "Terms of Use", "and": "And", "privacyPolicy": "Privacy Policy", "termsOfUseAndPrivacyPolicy": "Terms of Use and Privacy Policy", "sendEmailVerification": "Email reset code", "errorResettingPasswordEmailNotFound": "Error resetting password: <PERSON><PERSON> not found", "inputValidEmail": "Input a valid Email", "emailOTP": "Email OTP", "newPasscode": "New Passcode", "pleaseFillAllFields": "Please enter all required fields", "resetPass": "Reset Password", "continueWithApple": "Continue with Apple", "continueWithGoogle": "Continue with Google", "continueWithEmail": "Continue with <PERSON>ail", "verification": "Verification", "checkEmail": "Check email and enter the code", "cannotFindVerificationCode": "If you cannot find the verification code in your email inbox, please check your spam folder.", "completeSignUp": "Complete Sign Up", "verify": "Verify", "custom": "Custom", "textToVideo": "Text to Video", "pleaseEnterPrompt": "Please enter the prompt", "pleaseEnterPromptImage": "Please enter a prompt and select an image", "properGuidanceValue": "Please enter a proper guidance value", "keepImageSize": "Keep Image Size", "imageToVideo": "Image to Video", "copy": "Copy", "strength": "Strength", "imageIndex": "Image {}", "morphMix": "MorphMix", "uploadFaceVideo": "Upload Face Video", "faceVideo": "Face Video", "faceImage": "Face Image", "taskType": "Task Type", "videoToGif": "Video to gif", "extractVideoMp3": "Extract video mp3", "imagesToMp4": "Images to mp4", "imagesToGif": "Images to gif", "videoToFrames": "Video to frames", "reverseVideo": "Reverse video", "bounceVideo": "Bounce video (Boomerang)", "uploadWillCostNStandardTokens": "Upload each audio will cost 5 standard credits", "saveAsTemplate": "Save as Template", "generateVoiceEM": "✨ Generate Voice", "target": "Target", "logOut": "Log Out", "aboutUs": "About Us", "aboutUsDesc1": "Aethia AI is an innovative internet company, focused on providing high-quality internet services and intelligent products. We are dedicated to leveraging advanced technology and artificial intelligence to deliver exceptional experiences and solutions to our users.", "aboutUsDesc2": "Our team consists of passionate and experienced professionals with deep knowledge and skills in the fields of internet and mobile app development. We strive for excellence, continuously innovating and pushing boundaries to deliver creative and cutting-edge solutions across different industries.", "aboutUsDesc3": "Our mission is to change the world through the power of technology and provide users with convenient, intelligent, and innovative products and services. We constantly challenge ourselves, aiming to exceed user expectations and create a better digital world.", "aboutUsDesc4": "Our core values are customer-centricity, teamwork, innovation pursuit, and social responsibility. We always prioritize customer needs and satisfaction, actively listen and provide feedback, and continuously improve and enhance the user experience.", "aboutUsDesc5": "If you would like to learn more about us or explore partnership opportunities, please feel free to contact us. We look forward to working together with you to create a brighter future!", "aboutUsDesc6": "If you would like to learn more about us or explore partnership opportunities, please feel free to contact us. We look forward to working together with you to create a brighter future!", "email": "Email", "deleteAccountTitle": "Delete Account", "feedback": "<PERSON><PERSON><PERSON>", "letUsKnow": "Let us know", "submit": "Submit", "feedbackSentSuccessfully": "Feed<PERSON> Successfully", "feedbackIsRequired": "Feedback is required", "langSelect": "Select Language", "pleaseTypeYourEmail": "Please type your email.", "pleaseTypePassword": "Please type your password.", "yourPassAtLeast8": "Your password must be at least 8 characters, include number, uppercase letter and symbol character.", "downloadSuccessfully": "Download Successfully", "success": "Success", "yourGeneratedVoiceSavedIOS": "Your generated voice is saved successfully in the {} folder", "yourGeneratedFileSavedAndroid": "Your generated file is saved successfully in the Downloads folder", "yourGeneratedVoiceSavedAndroid": "Your generated voice is saved successfully in the Downloads folder", "yourGeneratedFileSavedIOS": "Your generated file is saved successfully in the {} folder", "yourMasterpieceIsProcessing": "Your masterpiece is processing...\nFeel free to check it out in My Creations later.", "notice": "Notice", "notNow": "Not now", "settings": "Settings", "continueT": "Continue", "selectAnImage": "Select an Image", "selectAVideo": "Select a Video", "or": "Or", "fromURL": "From URL", "pasteImageURL": "Paste image URL\nExample, https://example.com/image.jpg", "paste": "Paste", "uRLNotFromImage": "Please enter a valid URL", "add": "Add", "pleaseTryAnotherImage": "Please try another image!", "areYouSureDeleteTemplate": "Are you sure you want to delete template?", "selectCategory": "Select category", "pleaseGrantPermission": "Please grant permission", "pleaseAllowFullPhotosAccess": "Please allow full Photos access", "toUploadPhotoIos": "To upload photo, allow {} to access your device's photos.\nTap Settings>Aethia, and turn \"Photos\" on.", "toUploadCameraIos": "To upload photo, allow {} to access your device's camera.\nTap Settings>Aethia, and turn \"Camera\" on.", "toUploadCameraAndroid": "To upload photo, allow {} to access your device's camera.\nTap Settings>Permissions, and turn \"Camera\" on.", "toUploadPhotoAndroid": "To upload photo, allow {} to access your device's photos.\nTap Settings>Permissions, and turn \"Storage\" on.", "toUploadPhotoAndroid33": "To upload photo, allow {} to access your device's photos.\nTap Settings>Permissions, and turn \"Photos and videos'\" on.", "pleaseAllowCameraEditAvatar": "Please allow camera permission to edit avatar.", "pleasePickShorterVideo": "Please pick a shorter video", "grantPermission": "Grant Permission", "toUploadVideosIos": "To upload video, allow {} to access your device's photos.\nTap Settings>Aethia, and turn \"Photos\" on.", "toUploadVideosAndroid": "To upload video, allow {} to access your device's photos.\nTap Settings>Permissions, and turn \"Storage\" on.", "toUploadVideosAndroid33": "To upload video, allow {} to access your device's photos.\nTap Settings>Permissions, and turn \"Photos and videos'\" on.", "pickShorterAudio": "Pick a shorter audio", "pleaseAllowStorageAccess": "Please allow \"Storage\" access", "pleaseAllowMusicAccess": "Please allow \"Music and audio\" access", "toUploadAudioIos": "To upload audio, allow {} to access your device's audios.\\nTap Settings>Aethia, and turn \"Storage\" on.", "toUploadAudioAndroid": "To upload audio, allow {} to access your device's audios.\nTap Settings>Permissions, and turn \"Storage\" on.", "toUploadAudioAndroid33": "To upload audio, allow <PERSON><PERSON><PERSON> to access your device's audios.\nTap Settings>Permissions, and turn \"Music and audio'\" on.", "camera": "Camera", "editImage": "Edit Image", "video": "Video", "savedSpeech": "Saved Speech", "gallery": "Gallery", "info": "Info", "deleteDesc": "Please let us know the reason if you’d like to delete your account", "accountStatus": "Status: Deletion Processing\n\nNote: Your account will be scheduled for deletion in {} days. Please reach out to customer service if you did not initiate this action.\n", "undoDelete": "Undo Delete", "deleteAccount": "Account deletion initiated", "emailIsRequired": "Email is required", "deleteAccountDesc": "Are you certain you want to delete the account? All data associated with this account will be permanently erased.", "cancel": "Cancel", "aiHug": "AI Hug", "separatedImages": "Separated Images", "singleImage": "Single Image", "forBestResult": "For the best result:", "positionFacesClosely": "- Position faces closely together with consistent sizes.", "ensurePeopleFaceEachOther": "- Ensure the people face each other, not away.", "avoidExcessiveZoom": "- Avoid excessive zoom or distance", "keepImageBgSimilar": "- Keep image backgrounds similar", "exampleHug": "Example: Hug", "pleaseSelectFirstSecondPersonImage": "Please select first and second person image", "pleaseUploadImageWithPeople": "Please upload an image with two people", "hug": "<PERSON>g", "createAHuggingVideo": "Create a hugging video", "kiss": "Kiss", "createVideoTwoPeopleKissEachOther": "Create a video of two people kiss each other", "handshake": "Handshake", "createVideoTwoPeopleShakingHands": "Create a video of two people shake hands.", "dancing": "Dancing", "createDancingVideo": "Create a dancing video", "customContentInTheVideo": "Create custom video from prompt", "anImageWithTwoPeople": "An image with two people", "tapToSelect": "Tap to select", "firstPerson": "First Person", "secondPerson": "Second Person", "caption": "Caption", "scriptWriter": "<PERSON><PERSON><PERSON> Writer", "videoToolkit": "Video Toolkit", "livePortrait": "Live Portrait", "imageRetalker": "Image Retalker", "videoRetalker": "Video Retalker", "videoExtend": "Video Extend", "cartoonifyVideo": "Cartoonify Video", "voiceGeneration": "Voice Generation", "musicGeneration": "Music Generation", "uploadEndingImage": "Upload Ending Image", "endingImage": "Ending Image", "detailedDescriptionVideoAstronaut": "Detailed description of the video.\nExample: an astronaut riding a horse", "ratio": "<PERSON><PERSON>", "negative": "Negative", "enhancePrompt": "Enhance Prompt", "durationSeconds": "Duration (seconds)", "vertical": "Vertical", "widescreen": "Widescreen", "aiIsBusy": "AI is busy, please try later.", "turbo": "Turbo", "videoToVideo": "Video to Video", "addSound": "Add Sound", "extendVideo": "Extend Video", "soundPromptStar": "Sound Prompt*", "describeTheSoundForVideo": "Describe the sound for the video", "keepVideoSize": "Keep Video Size"}