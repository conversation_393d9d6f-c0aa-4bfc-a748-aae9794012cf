// transition_picker.dart
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class TransitionPicker extends StatefulWidget {
  final Function(TransitionType) onTransitionSelected;
  final TransitionType currentTransition;

  const TransitionPicker({
    super.key,
    required this.onTransitionSelected,
    required this.currentTransition,
  });

  @override
  State<TransitionPicker> createState() => _TransitionPickerState();
}

class _TransitionPickerState extends State<TransitionPicker> {
  late TransitionType selectedTransitionType;

  @override
  void initState() {
    super.initState();
    selectedTransitionType = widget.currentTransition;
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.black,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(10.0),
            child: Text(
              "Add Transition",
              style: TextStyle(fontSize: 20.0),
            ),
          ),
          SizedBox(
            height: 200,
            child: Column(
              children: [
                if (selectedTransitionType == TransitionType.none)
                  SizedBox(
                    height: 150,
                    width: 150,
                  )
                else
                  Image.asset(
                    'assets/gifs/${selectedTransitionType.toString().split('.').last}.gif',
                    height: 150,
                    width: 150,
                  ),
                SizedBox(height: 5),
                Expanded(
                  child: ListView.separated(
                    shrinkWrap: true,
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    scrollDirection: Axis.horizontal,
                    itemCount: TransitionType.values.length,
                    separatorBuilder: (context, index) {
                      return SizedBox(width: 40);
                    },
                    itemBuilder: (context, index) {
                      final transition = TransitionType.values[index];
                      final color = transition.name == selectedTransitionType.name ? Colors.deepPurple : null;
                      return GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () => setState(() => selectedTransitionType = transition),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.animation,
                                  color: color,
                                ),
                                SizedBox(width: 10),
                                Text(
                                  transition.toString().split('.').last,
                                  style: TextStyle(
                                    color: color,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: Text("Cancel"),
              ),
              ElevatedButton(
                onPressed: () {
                  widget.onTransitionSelected(selectedTransitionType);
                  Navigator.pop(context);
                },
                child: Text("Select"),
              )
            ],
          ),
        ],
      ),
    );
  }
}

enum TransitionType {
  none,
  fade,
  fadeblack,
  fadewhite,
  distance,
  wipeleft,
  wiperight,
  wipeup,
  wipedown,
  slideleft,
  slideright,
  slideup,
  slidedown,
  smoothleft,
  smoothright,
  smoothup,
  smoothdown,
  circlecrop,
  rectcrop,
  circleclose,
  circleopen,
  horzclose,
  horzopen,
  vertclose,
  vertopen,
  diagbl,
  diagbr,
  diagtl,
  diagtr,
  fadegrays,
  squeezev,
  squeezeh,
  zoomin,
}

class TransitionPreview extends StatelessWidget {
  final TransitionType type;
  final VideoPlayerController controller;

  const TransitionPreview({
    Key? key,
    required this.type,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Implementation of transition preview effects
    switch (type) {
      case TransitionType.fade:
        return FadeTransition(
          opacity: controller.value.isPlaying ? const AlwaysStoppedAnimation(0.5) : const AlwaysStoppedAnimation(1.0),
          child: VideoPlayer(controller),
        );
      // Add other transition effects
      default:
        return Container();
    }
  }
}
