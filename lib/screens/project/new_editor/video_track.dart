import 'dart:async';
import 'dart:io';

import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/custom_trim_slider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:ai_video_creator_editor/components/track_options.dart';
import 'package:ai_video_creator_editor/enums/track_type.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';

class VideoTrack extends StatefulWidget {
  const VideoTrack({
    super.key,
    required this.videoTrack,
    required this.index,
    required this.isSelected,
    required this.selectedTrackBorderColor,
  });

  final VideoTrackModel videoTrack;
  final int index;
  final bool isSelected;
  final Color selectedTrackBorderColor;

  @override
  State<VideoTrack> createState() => _VideoTrackState();
}

class _VideoTrackState extends State<VideoTrack>
    with AutomaticKeepAliveClientMixin {
  final ValueNotifier<List<File>> _thumbnailNotifier =
      ValueNotifier<List<File>>([]);
  bool _isGeneratingThumbnails = false;

  // Trim functionality
  TrimBoundaries _boundary = TrimBoundaries.none;
  double _trimStart = 0.0;
  double _trimEnd = 0.0;
  Timer? _debounceTimer;

  OverlayEntry? _overlayEntry;
  final GlobalKey _trackKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _trimStart = widget.videoTrack.videoTrimStart;
    _trimEnd = widget.videoTrack.videoTrimEnd;
    setState(() {
      _isGeneratingThumbnails = true;
    });
    _generateThumbnailAtTime(widget.videoTrack.processedFile.path);
  }

  @override
  void didUpdateWidget(VideoTrack oldWidget) {
    super.didUpdateWidget(oldWidget);

    print("=== VideoTrack didUpdateWidget called ===");
    print("Old file: ${oldWidget.videoTrack.processedFile.path}");
    print("New file: ${widget.videoTrack.processedFile.path}");
    print("Old trim start: ${oldWidget.videoTrack.videoTrimStart}");
    print("New trim start: ${widget.videoTrack.videoTrimStart}");
    print("Old trim end: ${oldWidget.videoTrack.videoTrimEnd}");
    print("New trim end: ${widget.videoTrack.videoTrimEnd}");

    // Regenerate thumbnails if the processed file has changed (e.g., after trimming)
    if (oldWidget.videoTrack.processedFile.path !=
            widget.videoTrack.processedFile.path ||
        oldWidget.videoTrack.videoTrimStart !=
            widget.videoTrack.videoTrimStart ||
        oldWidget.videoTrack.videoTrimEnd != widget.videoTrack.videoTrimEnd) {
      print("Track changed, regenerating thumbnails...");
      setState(() {
        _isGeneratingThumbnails = true;
      });
      _generateThumbnailAtTime(widget.videoTrack.processedFile.path);
    } else {
      print("No track changes detected, skipping thumbnail regeneration");
    }
  }

  Future<void> _generateThumbnailAtTime(String filePath) async {
    try {
      final Directory tempDir = await getTemporaryDirectory();

      print("=== Generating thumbnails for track ${widget.videoTrack.id} ===");
      print("File path: $filePath");
      print("Track trim start: ${widget.videoTrack.videoTrimStart}");
      print("Track trim end: ${widget.videoTrack.videoTrimEnd}");
      print("Track total duration: ${widget.videoTrack.totalDuration}");

      // Clear old thumbnails for this track first
      await _clearOldThumbnails(tempDir);

      final String outputPattern =
          '${tempDir.path}/video_track${widget.index}_${widget.videoTrack.id}_${widget.videoTrack.lastModified.millisecondsSinceEpoch}_frame_%d.jpg';

      // For trimmed videos, we want to show thumbnails from the trimmed content
      // Extract thumbnails only from the trimmed segment
      final trimDuration = widget.videoTrack.videoTrimEnd - widget.videoTrack.videoTrimStart;
      final startTime = widget.videoTrack.videoTrimStart;
      
      final command =
          '-ss $startTime -i "$filePath" -t $trimDuration -vf "fps=1,scale=160:90" -q:v 2 "$outputPattern"';

      print("Generating thumbnails with command: $command");

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      print("Thumbnail generation return code: $returnCode");

      if (!ReturnCode.isSuccess(returnCode)) {
        print("Failed to generate thumbnails for ${widget.videoTrack.id}");
        if (mounted) _thumbnailNotifier.value = [];
        return;
      }

      final List<File> files = (await tempDir.list().where((entity) {
        return entity.path.contains(
            "video_track${widget.index}_${widget.videoTrack.id}_${widget.videoTrack.lastModified.millisecondsSinceEpoch}");
      }).map((entity) {
        return File(entity.path);
      }).toList())
        ..sort((a, b) {
          int extractNumber(String path) {
            String fileName = p.basename(path);
            final index =
                int.tryParse(fileName.split("_").last.split(".").first) ?? 0;
            return index;
          }

          return extractNumber(a.path).compareTo(extractNumber(b.path));
        });

      print(
          "Generated ${files.length} thumbnails for track ${widget.videoTrack.id}");
      if (mounted) {
        _thumbnailNotifier.value = files;
        setState(() {
          _isGeneratingThumbnails = false;
        });
      }
    } catch (e) {
      print("Error generating thumbnails: $e");
      if (mounted) {
        _thumbnailNotifier.value = [];
        setState(() {
          _isGeneratingThumbnails = false;
        });
      }
    }
  }

  Future<void> _clearOldThumbnails(Directory tempDir) async {
    try {
      // Clear ALL old thumbnails for this track (any timestamp)
      final oldFiles = await tempDir.list().where((entity) {
        return entity.path.contains(
                "video_track${widget.index}_${widget.videoTrack.id}_") &&
            entity.path.contains("_frame_");
      }).toList();

      for (final file in oldFiles) {
        if (file is File) {
          await file.delete();
        }
      }
      print(
          "Cleared ${oldFiles.length} old thumbnails for track ${widget.videoTrack.id}");
    } catch (e) {
      print("Error clearing old thumbnails: $e");
    }
  }


  void _onPanUpdate(DragUpdateDetails details, double trackWidth) {
    if (_boundary == TrimBoundaries.none || !widget.isSelected) return;

    final originalDuration = widget.videoTrack.originalDuration;
    if (originalDuration <= 0) return;

    // For drag sensitivity, we want consistent behavior regardless of trim state
    // Use a fixed scale based on original duration to maintain consistent drag feel
    final delta = details.delta.dx / (MediaQuery.of(context).size.width / 8) * 1.0; // 1 second per width/8 unit
    const double minTrimSize = 0.5; // Minimum 0.5 seconds
    
    // Set boundaries within the original video duration
    final double lowerLimit = 0;
    final double upperLimit = originalDuration;

    void updateTrimWithDebounce(double newStart, double newEnd) {
      setState(() {
        _trimStart = newStart;
        _trimEnd = newEnd;
      });

      // Cancel previous debounce timer
      _debounceTimer?.cancel();
      
      // Set new debounce timer for provider update
      _debounceTimer = Timer(const Duration(milliseconds: 300), () {
        context
            .read<VideoEditorProvider>()
            .updateVideoTrack(widget.index, _trimStart, _trimEnd);
      });
    }

    switch (_boundary) {
      case TrimBoundaries.start:
        updateTrimWithDebounce(
            (_trimStart + delta).clamp(lowerLimit, _trimEnd - minTrimSize),
            _trimEnd);
        break;

      case TrimBoundaries.end:
        updateTrimWithDebounce(_trimStart,
            (_trimEnd + delta).clamp(_trimStart + minTrimSize, upperLimit));
        break;

      case TrimBoundaries.inside:
        final length = _trimEnd - _trimStart;
        var newStart =
            (_trimStart + delta).clamp(lowerLimit, upperLimit - length);
        updateTrimWithDebounce(newStart, newStart + length);
        break;

      case TrimBoundaries.none:
        break;
    }
  }

  void _showOverlay(BuildContext context) {
    final RenderBox renderBox =
        _trackKey.currentContext?.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final provider = context.read<VideoEditorProvider>();

    // Don't show mute option for image-based videos
    final bool showMute = widget.videoTrack.hasOriginalAudio && !widget.videoTrack.isImageBased;
    final bool? isMuted = showMute ? provider.isVideoMuted(widget.videoTrack.id) : null;

    _overlayEntry = OverlayEntry(
      builder: (context) => TrackOptions(
        offset: offset,
        trackType: TrackType.video,
        onTap: _hideOverlay,
        onTrim: () {
          provider.setVideoTrackIndex(widget.index);
          _hideOverlay();
        },
        onDelete: () {
          provider.removeVideoTrack(widget.index);
          _hideOverlay();
        },
        onMute: showMute ? () {
          provider.toggleVideoMute(widget.videoTrack.id);
          _hideOverlay();
        } : null,
        showMute: showMute,
        isMuted: isMuted,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _thumbnailNotifier.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    // Calculate track width using the same approach as provider (use totalDuration from model)
    // This ensures consistency with timeline container calculations
    final width = MediaQuery.of(context).size.width;
    final trackWidth = (width / 8) * widget.videoTrack.totalDuration;
    
    return Container(
      width: trackWidth,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Main track container (no absolute positioning)
          Container(
            width: trackWidth,
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(
                color: widget.selectedTrackBorderColor,
                width: widget.isSelected ? 2 : 0,
              ),
            ),
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              key: _trackKey,
              onTap: () => _showOverlay(context),
              onHorizontalDragUpdate: (details) {
                _boundary = TrimBoundaries.inside;
                _onPanUpdate(details, trackWidth);
              },
              child: ValueListenableBuilder<List<File>>(
                valueListenable: _thumbnailNotifier,
                builder: (context, thumbnails, _) {
                  return Stack(
                    children: [
                      // Show loading indicator or thumbnails
                      _isGeneratingThumbnails
                          ? Container(
                              height: 60, // Match thumbnail height
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.7),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 14,
                                        height: 14,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            Colors.white.withValues(alpha: 0.9),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 6),
                                      Text(
                                        'Loading...',
                                        style: TextStyle(
                                          color: Colors.white
                                              .withValues(alpha: 0.9),
                                          fontSize: 11,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : Container(
                              height: 60,
                              child: thumbnails.isNotEmpty
                                  ? ListView.builder(
                                      shrinkWrap: true,
                                      padding: EdgeInsets.zero,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      scrollDirection: Axis.horizontal,
                                      itemCount: thumbnails.length,
                                      itemBuilder: (context, index) {
                                        return SizedBox(
                                          width: trackWidth / thumbnails.length,
                                          child: Image.file(
                                            thumbnails[index],
                                            fit: BoxFit.cover,
                                          ),
                                        );
                                      },
                                    )
                                  : const Center(
                                      child: Icon(Icons.broken_image_outlined)),
                            ),
                      // Duration and filename overlay
                      Positioned(
                        bottom: 2,
                        left: 4,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 4, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            "${widget.videoTrack.totalDuration}s",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
          
          // Start handle - positioned at track start (left boundary)
          if (widget.isSelected)
            Positioned(
              left: 0, // Always at track start since track width represents trimmed content
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragUpdate: (details) {
                  _boundary = TrimBoundaries.start;
                  _onPanUpdate(details, trackWidth);
                },
                child: Container(
                  width: 20,
                  height: 60,
                  decoration: BoxDecoration(
                    color: widget.selectedTrackBorderColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(4),
                      bottomLeft: Radius.circular(4),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(
                          Radius.circular(double.maxFinite),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          
          // End handle - positioned at track end (right boundary) 
          if (widget.isSelected)
            Positioned(
              right: 0, // Always at track end since track width represents trimmed content
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragUpdate: (details) {
                  _boundary = TrimBoundaries.end;
                  _onPanUpdate(details, trackWidth);
                },
                child: Container(
                  width: 20,
                  height: 60,
                  decoration: BoxDecoration(
                    color: widget.selectedTrackBorderColor,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(
                          Radius.circular(double.maxFinite),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
