import 'package:ai_video_creator_editor/components/track_options.dart';
import 'package:ai_video_creator_editor/enums/track_type.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/custom_trim_slider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/text_style_editor.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

class TextTrack extends StatefulWidget {
  const TextTrack({
    super.key,
    required this.textTrack,
    required this.index,
    required this.isSelected,
    required this.timelineWidth,
    required this.timelineDuration,
    required this.selectedTrackBorderColor,
    this.previewHeight,
  });

  final TextTrackModel textTrack;
  final int index;
  final bool isSelected;
  final double timelineWidth;
  final double timelineDuration;
  final Color selectedTrackBorderColor;
  final double? previewHeight;

  @override
  State<TextTrack> createState() => _TextTrackState();
}

class _TextTrackState extends State<TextTrack>
    with AutomaticKeepAliveClientMixin {
  VideoPlayerController? _videoPlayerController;

  TrimBoundaries _boundary = TrimBoundaries.none;
  Rect _trimRect = Rect.zero;
  double _trimStart = 0.0;
  double _trimEnd = 0.0;

  bool isVideoPositionOnTextTrack = false;

  OverlayEntry? _overlayEntry;
  final GlobalKey _trackKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _trimStart = widget.textTrack.trimStartTime;
    _trimEnd = widget.textTrack.trimEndTime;
    _videoPlayerController =
        context.read<VideoEditorProvider>().videoEditorController?.video;
    _videoPlayerController?.addListener(_syncTextWithVideo);
  }

  Future<void> _syncTextWithVideo() async {
    if (_videoPlayerController == null) return;

    final videoState = _videoPlayerController!.value;
    final videoPosition = videoState.position.inMilliseconds;
    final startTime = widget.textTrack.trimStartTime * 1000;
    final endTime = widget.textTrack.trimEndTime * 1000;
    final provider = context.read<VideoEditorProvider>();

    if (videoPosition >= startTime && videoPosition < endTime) {
      isVideoPositionOnTextTrack = true;
      await provider.updateDisplayText(widget.textTrack.text);
    } else if (isVideoPositionOnTextTrack) {
      isVideoPositionOnTextTrack = false;
      await provider.updateDisplayText("");
    }
  }

  Rect _getTrimRect() {
    double left = (_trimStart / widget.timelineDuration) * widget.timelineWidth;
    double right = (_trimEnd / widget.timelineDuration) * widget.timelineWidth;

    left = left.isNaN ? 0 : left;
    right = right.isNaN ? 0 : right;

    return Rect.fromLTRB(left, 0, right, 30);
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_boundary == TrimBoundaries.none || !widget.isSelected) return;

    final textTracks = context.read<VideoEditorProvider>().textTracks;
    final isFirstTrack = widget.index == 0;
    final isLastTrack = widget.index == textTracks.length - 1;

    final double lowerLimit = (textTracks.length == 1 || isFirstTrack)
        ? 0
        : textTracks[widget.index - 1].trimEndTime;

    final double upperLimit = (textTracks.length == 1 || isLastTrack)
        ? widget.timelineDuration.toDouble()
        : textTracks[widget.index + 1].trimStartTime;

    final delta =
        details.delta.dx / widget.timelineWidth * widget.timelineDuration;
    const double minTrimSize = 1;

    void updateTrim(double newStart, double newEnd) {
      _trimStart = newStart;
      _trimEnd = newEnd;
      context
          .read<VideoEditorProvider>()
          .updateTextTrack(widget.index, _trimStart, _trimEnd);
    }

    switch (_boundary) {
      case TrimBoundaries.start:
        updateTrim(
            (_trimStart + delta).clamp(lowerLimit, _trimEnd - minTrimSize),
            _trimEnd);
        break;

      case TrimBoundaries.end:
        updateTrim(_trimStart,
            (_trimEnd + delta).clamp(_trimStart + minTrimSize, upperLimit));
        break;

      case TrimBoundaries.inside:
        final length = _trimEnd - _trimStart;
        var newStart =
            (_trimStart + delta).clamp(lowerLimit, upperLimit - length);
        updateTrim(newStart, newStart + length);
        break;

      case TrimBoundaries.none:
        break;
    }
  }

  @override
  void dispose() {
    _videoPlayerController?.removeListener(_syncTextWithVideo);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    _trimRect = _getTrimRect();
    return SizedBox(
      width: widget.timelineWidth,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned.fromRect(
            rect: _trimRect,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.selectedTrackBorderColor,
                  width: 2,
                ),
              ),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                key: _trackKey,
                onTap: () => _showOverlay(context),
                onHorizontalDragUpdate: (details) {
                  _boundary = TrimBoundaries.inside;
                  _onPanUpdate(details);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  alignment: Alignment.centerLeft,
                  child: ClipRect(
                    child: Text(
                      "${(_trimEnd - _trimStart).toStringAsFixed(1)} | ${widget.textTrack.text}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      softWrap: false,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ),
            ),
          ),

          if (widget.isSelected)
            Positioned(
              left: _trimRect.left - 10,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragUpdate: (details) {
                  _boundary = TrimBoundaries.start;
                  _onPanUpdate(details);
                },
                child: Container(
                  width: 20,
                  height: 30,
                  decoration: BoxDecoration(
                    color: widget.selectedTrackBorderColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(4),
                      bottomLeft: Radius.circular(4),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(
                          Radius.circular(double.maxFinite),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // End handle
          if (widget.isSelected)
            Positioned(
              left: _trimRect.right - 10,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragUpdate: (details) {
                  _boundary = TrimBoundaries.end;
                  _onPanUpdate(details);
                },
                child: Container(
                  width: 20,
                  height: 30,
                  decoration: BoxDecoration(
                    color: widget.selectedTrackBorderColor,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(
                          Radius.circular(double.maxFinite),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showOverlay(BuildContext context) {
    final RenderBox renderBox =
        _trackKey.currentContext?.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => TrackOptions(
        offset: offset,
        trackType: TrackType.text,
        onTap: _hideOverlay,
        onTrim: () {
          context.read<VideoEditorProvider>().setTextTrackIndex(widget.index);
          _hideOverlay();
        },
        onDelete: () {
          context.read<VideoEditorProvider>().removeTextTrack(widget.index);
          _hideOverlay();
        },
        onEditStyle: () {
          _hideOverlay();
          _showStyleEditor(context);
        },
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _showStyleEditor(BuildContext context) {
    print('TextStyleEditor: _showStyleEditor called');

    // Use dynamic preview height or fallback to fixed height
    final deviceWidth = MediaQuery.of(context).size.width;
    final dynamicHeight =
        widget.previewHeight ?? 370.0; // Use passed height or fallback

    final previewSize = Size(deviceWidth, dynamicHeight);

    print(
        'TextStyleEditor: Using dynamic bounds - ${previewSize.width}x${previewSize.height}');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => TextStyleEditor(
        textTrack: widget.textTrack,
        previewSize: previewSize,
        onStyleUpdated: (updatedTrack) {
          context.read<VideoEditorProvider>().updateTextTrackModel(
                widget.index,
                updatedTrack,
              );
        },
      ),
    );
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  bool get wantKeepAlive => true;
}
