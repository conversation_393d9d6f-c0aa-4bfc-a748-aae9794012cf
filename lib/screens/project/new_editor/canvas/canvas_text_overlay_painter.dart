import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/utils/text_auto_wrap_helper.dart';

/// Simplified text overlay painter for canvas-based rendering
/// 
/// This painter is designed for use within transformed canvas contexts
/// where video transformations are already applied to the canvas.
/// It uses simplified coordinate mapping compared to the full TextOverlayPainter.
class CanvasTextOverlayPainter extends CustomPainter {
  final List<TextTrackModel> textTracks;
  final double currentTime;
  final Size containerSize;
  final Size videoSize;
  final bool isGlobal; // true for global text, false for per-video text
  final int? selectedTextTrackIndex;

  CanvasTextOverlayPainter({
    required this.textTracks,
    required this.currentTime,
    required this.containerSize,
    required this.videoSize,
    this.isGlobal = false,
    this.selectedTextTrackIndex,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (int i = 0; i < textTracks.length; i++) {
      final track = textTracks[i];
      if (_isTrackVisible(track, currentTime)) {
        _drawTextTrack(canvas, track, size, i == selectedTextTrackIndex);
      }
    }
  }

  bool _isTrackVisible(TextTrackModel track, double currentTime) {
    final startTime = track.trimStartTime;
    final endTime = track.trimEndTime;
    return currentTime >= startTime && currentTime <= endTime;
  }

  void _drawTextTrack(Canvas canvas, TextTrackModel track, Size size, bool isSelected) {
    // Calculate font size with simple scaling
    final fontSize = _calculateFontSize(track.fontSize);
    
    // Create text style
    final textStyle = TextStyle(
      color: track.textColor,
      fontSize: fontSize,
      fontFamily: track.fontFamily,
      height: 1.0,
      shadows: [
        Shadow(
          offset: const Offset(1, 1),
          blurRadius: 2,
          color: Colors.black.withValues(alpha: 0.5),
        ),
      ],
    );

    // Calculate text position
    final position = _calculateTextPosition(track, size);
    
    // Handle text wrapping
    final availableSize = _calculateAvailableSize(track, size, position);
    final wrappedLines = TextAutoWrapHelper.wrapTextToFit(
      track.text,
      availableSize.width,
      availableSize.height,
      textStyle,
    );

    // Draw text with rotation if needed
    if (track.rotation != 0) {
      _drawRotatedText(canvas, wrappedLines, textStyle, position, track.rotation);
    } else {
      _drawNormalText(canvas, wrappedLines, textStyle, position);
    }

    // Draw selection box if selected
    if (isSelected) {
      _drawSelectionBox(canvas, wrappedLines, textStyle, position, track.rotation);
    }
  }

  double _calculateFontSize(double baseFontSize) {
    if (isGlobal) {
      // Global text: scale based on canvas size
      final scale = math.min(containerSize.width / 800, containerSize.height / 600);
      return baseFontSize * scale.clamp(0.5, 2.0);
    } else {
      // Per-video text: scale based on video size relative to container
      final scaleX = containerSize.width / videoSize.width;
      final scaleY = containerSize.height / videoSize.height;
      final scale = math.min(scaleX, scaleY);
      return baseFontSize * scale.clamp(0.5, 3.0);
    }
  }

  Offset _calculateTextPosition(TextTrackModel track, Size size) {
    if (isGlobal) {
      // Global text: use absolute canvas coordinates
      return Offset(
        track.position.dx,
        track.position.dy,
      );
    } else {
      // Per-video text: use normalized coordinates relative to video size
      return Offset(
        (track.position.dx / videoSize.width) * size.width,
        (track.position.dy / videoSize.height) * size.height,
      );
    }
  }

  Size _calculateAvailableSize(TextTrackModel track, Size containerSize, Offset position) {
    // Calculate available space from position to container edge
    final availableWidth = containerSize.width - position.dx - 20; // 20px margin
    final availableHeight = containerSize.height - position.dy - 20; // 20px margin
    
    return Size(
      availableWidth.clamp(100.0, containerSize.width),
      availableHeight.clamp(50.0, containerSize.height),
    );
  }

  void _drawRotatedText(Canvas canvas, List<String> lines, TextStyle style, 
                       Offset position, double rotation) {
    // Calculate text dimensions
    final textWidth = _calculateMaxLineWidth(lines, style);
    final textHeight = _calculateTextHeight(lines, style);
    
    // Calculate rotation center
    final rotationCenter = Offset(
      position.dx + textWidth / 2,
      position.dy + textHeight / 2,
    );
    
    canvas.save();
    canvas.translate(rotationCenter.dx, rotationCenter.dy);
    canvas.rotate(rotation * math.pi / 180);
    canvas.translate(-textWidth / 2, -textHeight / 2);
    
    _drawNormalText(canvas, lines, style, Offset.zero);
    
    canvas.restore();
  }

  void _drawNormalText(Canvas canvas, List<String> lines, TextStyle style, Offset position) {
    double currentY = position.dy;
    
    for (final line in lines) {
      final textSpan = TextSpan(text: line, style: style);
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.left,
      );
      
      textPainter.layout();
      textPainter.paint(canvas, Offset(position.dx, currentY));
      
      currentY += textPainter.height;
      textPainter.dispose();
    }
  }

  void _drawSelectionBox(Canvas canvas, List<String> lines, TextStyle style, 
                        Offset position, double rotation) {
    final textWidth = _calculateMaxLineWidth(lines, style);
    final textHeight = _calculateTextHeight(lines, style);
    
    final selectionRect = Rect.fromLTWH(
      position.dx - 2,
      position.dy - 2,
      textWidth + 4,
      textHeight + 4,
    );
    
    final borderPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    if (rotation != 0) {
      final rotationCenter = Offset(
        position.dx + textWidth / 2,
        position.dy + textHeight / 2,
      );
      
      canvas.save();
      canvas.translate(rotationCenter.dx, rotationCenter.dy);
      canvas.rotate(rotation * math.pi / 180);
      canvas.translate(-textWidth / 2, -textHeight / 2);
      
      final rotatedRect = Rect.fromLTWH(-2, -2, textWidth + 4, textHeight + 4);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rotatedRect, const Radius.circular(4.0)),
        borderPaint,
      );
      
      canvas.restore();
    } else {
      canvas.drawRRect(
        RRect.fromRectAndRadius(selectionRect, const Radius.circular(4.0)),
        borderPaint,
      );
    }
  }

  double _calculateMaxLineWidth(List<String> lines, TextStyle style) {
    double maxWidth = 0;
    for (final line in lines) {
      final textPainter = TextPainter(
        text: TextSpan(text: line, style: style),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      maxWidth = math.max(maxWidth, textPainter.width);
      textPainter.dispose();
    }
    return maxWidth;
  }

  double _calculateTextHeight(List<String> lines, TextStyle style) {
    return TextAutoWrapHelper.calculateWrappedTextHeight(lines, style);
  }

  @override
  bool shouldRepaint(CanvasTextOverlayPainter oldDelegate) {
    return oldDelegate.textTracks != textTracks ||
           oldDelegate.currentTime != currentTime ||
           oldDelegate.containerSize != containerSize ||
           oldDelegate.videoSize != videoSize ||
           oldDelegate.isGlobal != isGlobal ||
           oldDelegate.selectedTextTrackIndex != selectedTextTrackIndex;
  }
}