import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/canvas_transform.dart';
import 'sequential_text_overlay_painter.dart';
import 'crop_preview_widget.dart';
import 'dart:ui' as ui;
import 'dart:math' as math;

/// Unified clipper for cropping both video and image content
/// Based on the working _VideoCropClipper from IndividualVideoCropDialog
class MediaCropClipper extends CustomClipper<Rect> {
  final Rect cropRect;

  MediaCropClipper(this.cropRect);

  @override
  Rect getClip(Size size) {
    return Rect.fromLTWH(
      cropRect.left * size.width,
      cropRect.top * size.height,
      cropRect.width * size.width,
      cropRect.height * size.height,
    );
  }

  @override
  bool shouldReclip(MediaCropClipper oldClipper) {
    return oldClipper.cropRect != cropRect;
  }
}

/// Unified renderer for media (video/image) with canvas transformations
class MediaCanvasRenderer extends StatefulWidget {
  final VideoTrackModel track;
  final VideoPlayerController? controller;
  final Size canvasSize;
  final bool isSelected;
  final bool showHandles;
  final Function(VideoTrackModel)? onTrackUpdate;
  final VoidCallback? onTap;

  // Text overlay support
  final List<TextTrackModel> textTracks;
  final double currentTime;
  final int? selectedTextIndex;

  const MediaCanvasRenderer({
    super.key,
    required this.track,
    required this.controller,
    required this.canvasSize,
    this.isSelected = false,
    this.showHandles = false,
    this.onTrackUpdate,
    this.onTap,
    this.textTracks = const [],
    required this.currentTime,
    this.selectedTextIndex,
  });

  @override
  State<MediaCanvasRenderer> createState() => _MediaCanvasRendererState();
}

class _MediaCanvasRendererState extends State<MediaCanvasRenderer> {
  ui.Image? _imageCache;
  bool _isLoadingImage = false;

  @override
  void initState() {
    super.initState();
    if (widget.track.isImageBased) {
      _loadImage();
    }
  }

  @override
  void didUpdateWidget(MediaCanvasRenderer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.track.originalFile.path != oldWidget.track.originalFile.path) {
      _imageCache = null;
      if (widget.track.isImageBased) {
        _loadImage();
      }
    }
  }

  Future<void> _loadImage() async {
    if (_isLoadingImage || _imageCache != null) return;

    setState(() => _isLoadingImage = true);

    try {
      final bytes = await widget.track.originalFile.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();

      if (mounted) {
        setState(() {
          _imageCache = frame.image;
          _isLoadingImage = false;
        });
      }
    } catch (e) {
      print('Error loading image: $e');
      if (mounted) {
        setState(() => _isLoadingImage = false);
      }
    }
  }

  @override
  void dispose() {
    _imageCache?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final transform = _getCanvasTransform();

    print('🎨 MediaCanvasRenderer.build() for track: ${widget.track.id}');
    print('   Container canvas size: ${widget.canvasSize}');
    print('   Track canvas size: ${widget.track.canvasSize}');
    print('   Transform size: ${transform.size}');
    print('   Transform position: ${transform.position}');
    print(
        '   Controller initialized: ${widget.controller?.value.isInitialized}');
    print('   Is image based: ${widget.track.isImageBased}');

    // Check if track still has tiny default canvas properties (shouldn't happen anymore)
    if (widget.track.canvasSize == const Size(100, 100)) {
      print(
          '   ⚠️ Track still has default tiny canvas size - this should not happen anymore');
      print('   Showing loading indicator');
      return Container(
        width: widget.canvasSize.width,
        height: widget.canvasSize.height,
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.white54),
              SizedBox(height: 8),
              Text('Initializing...',
                  style: TextStyle(color: Colors.white54, fontSize: 12)),
            ],
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: widget.canvasSize.width,
        height: widget.canvasSize.height,
        child: Stack(
          children: [
            // Media content
            _buildMediaContent(),

            // Text overlays
            _buildTextOverlays(),

            // Selection overlay
            if (widget.isSelected) _buildSelectionOverlay(),

            // Manipulation handles
            if (widget.showHandles) _buildManipulationHandles(),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaContent() {
    final transform = _getCanvasTransform();

    return Positioned(
      left: transform.position.dx,
      top: transform.position.dy,
      child: Transform(
        alignment: Alignment.center,
        transform: Matrix4.identity()
          ..scale(transform.scale)
          ..rotateZ(transform.rotation),
        child: Opacity(
          opacity: transform.opacity,
          child: ClipRect(
            child: Container(
              width: transform.size.width,
              height: transform.size.height,
              child: _buildMediaWidget(transform),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMediaWidget(CanvasTransform transform) {
    print('🔍 _buildMediaWidget() for track: ${widget.track.id}');
    print('   Transform size: ${transform.size}');
    print('   Is image based: ${widget.track.isImageBased}');

    // Safety check: Don't render if transform size is invalid or too small
    if (transform.size.width < 50 ||
        transform.size.height < 50 ||
        !transform.size.width.isFinite ||
        !transform.size.height.isFinite) {
      print('   ⚠️ SAFETY CHECK TRIGGERED - Invalid transform size');
      return Container(
        width: 50,
        height: 50,
        color: Colors.grey[800],
        child: Center(
          child: Icon(Icons.hourglass_empty, color: Colors.white54, size: 20),
        ),
      );
    }

    if (widget.track.isImageBased) {
      print('   → Taking IMAGE rendering path');
      // Render image
      if (_imageCache != null) {
        final imageSize =
            Size(_imageCache!.width.toDouble(), _imageCache!.height.toDouble());

        print('   🔍 IMAGE CROP INFO:');
        print(
            '      Image size: ${imageSize.width.toStringAsFixed(2)} x ${imageSize.height.toStringAsFixed(2)}');
        print(
            '      Transform size: ${transform.size.width.toStringAsFixed(2)} x ${transform.size.height.toStringAsFixed(2)}');
        print('      Crop model: ${widget.track.canvasCropModel}');

        // Use CropPreviewWidget approach with proper scaling
        Widget imageWidget = CustomPaint(
          size: transform.size,
          painter: _ImagePainter(
            image: _imageCache!,
            cropRect: const Rect.fromLTWH(
                0, 0, 1, 1), // Full image, crop handled by CropPreviewWidget
          ),
        );

        // Apply crop if enabled
        if (widget.track.canvasCropModel?.enabled == true) {
          return CropPreviewWidget(
            videoSize: imageSize,
            previewSize: transform.size,
            cropModel: widget.track.canvasCropModel,
            child: imageWidget,
          );
        }

        return imageWidget;
      } else if (_isLoadingImage) {
        return Center(child: CircularProgressIndicator());
      } else {
        return Container(
          color: Colors.grey[800],
          child: Center(
            child: Icon(Icons.image, color: Colors.white54),
          ),
        );
      }
    } else {
      print('   → Taking VIDEO rendering path');
      // Render video
      if (widget.controller != null && widget.controller!.value.isInitialized) {
        print('   ✅ Video controller is initialized');
        print('   Video controller size: ${widget.controller!.value.size}');
        print('   Rendering video with transform size: ${transform.size}');

        final videoSize = widget.controller!.value.size;

        print('   🔲 Video crop info for track ${widget.track.id}:');
        print('      Crop model: ${widget.track.canvasCropModel}');
        print(
            '      Original video size: ${videoSize.width} x ${videoSize.height}');

        final videoScale = _calculateVideoScale(transform, videoSize);

        // Create base video widget with scaling
        Widget videoWidget = SizedBox(
          width: videoSize.width * videoScale,
          height: videoSize.height * videoScale,
          child: VideoPlayer(widget.controller!),
        );

        // Apply crop if enabled using CropPreviewWidget
        if (widget.track.canvasCropModel?.enabled == true) {
          print('      🔲 Applying video crop using CropPreviewWidget');

          // Wrap the scaled video with CropPreviewWidget
          videoWidget = CropPreviewWidget(
            videoSize: videoSize,
            previewSize: transform.size,
            cropModel: widget.track.canvasCropModel,
            child: SizedBox(
              width: transform.size.width,
              height: transform.size.height,
              child: Center(child: videoWidget),
            ),
          );

          return videoWidget;
        }

        // For non-cropped video, center it in the transform area
        return SizedBox(
          width: transform.size.width,
          height: transform.size.height,
          child: ClipRect(
            child: Center(
              child: videoWidget,
            ),
          ),
        );
      } else {
        print('   ❌ Video controller not initialized');
        print('   Controller null: ${widget.controller == null}');
        if (widget.controller != null) {
          print(
              '   Controller initialized: ${widget.controller!.value.isInitialized}');
        }

        return Container(
          color: Colors.black,
          child: Center(
            child: Icon(Icons.videocam, color: Colors.white54),
          ),
        );
      }
    }
  }

  Widget _buildTextOverlays() {
    if (widget.textTracks.isEmpty) {
      return const SizedBox.shrink();
    }

    return CustomPaint(
      size: widget.canvasSize,
      painter: SequentialTextOverlayPainter(
        textTracks: widget.textTracks,
        currentTime: widget.currentTime,
        canvasSize: widget.canvasSize,
        currentVideoTrack: widget.track,
        selectedTextIndex: widget.selectedTextIndex,
      ),
    );
  }

  Widget _buildSelectionOverlay() {
    final transform = _getCanvasTransform();

    return Positioned(
      left: transform.position.dx,
      top: transform.position.dy,
      child: Transform(
        alignment: Alignment.center,
        transform: Matrix4.identity()
          ..scale(transform.scale)
          ..rotateZ(transform.rotation),
        child: Container(
          width: transform.size.width,
          height: transform.size.height,
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.blue,
              width: 2,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildManipulationHandles() {
    final transform = _getCanvasTransform();
    final corners = transform.cornerPoints;

    return Stack(
      children: [
        // Corner resize handles
        ...corners.map((corner) => Positioned(
              left: corner.dx - 6,
              top: corner.dy - 6,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.blue, width: 2),
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            )),

        // Rotation handle
        Positioned(
          left: transform.rotationHandlePosition.dx - 8,
          top: transform.rotationHandlePosition.dy - 8,
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.orange,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: Icon(
              Icons.rotate_right,
              size: 10,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  double _calculateVideoScale(CanvasTransform transform, Size videoSize) {
    if (videoSize.width <= 0 ||
        videoSize.height <= 0 ||
        !videoSize.width.isFinite ||
        !videoSize.height.isFinite) {
      return 1.0;
    }

    if (transform.size.width <= 0 ||
        transform.size.height <= 0 ||
        !transform.size.width.isFinite ||
        !transform.size.height.isFinite) {
      return 1.0;
    }

    // Calculate scale to fit the video into the transform size while maintaining aspect ratio
    final scaleX = transform.size.width / videoSize.width;
    final scaleY = transform.size.height / videoSize.height;
    final scale = math.min(scaleX, scaleY);

    // Safety check for the calculated scale
    if (!scale.isFinite || scale <= 0) {
      return 1.0;
    }

    print('   📐 Video scale calculation:');
    print('      Video size: ${videoSize.width} x ${videoSize.height}');
    print(
        '      Transform size: ${transform.size.width} x ${transform.size.height}');
    print('      Scale X: $scaleX, Scale Y: $scaleY');
    print('      Final scale: $scale');

    return scale;
  }

  CanvasTransform _getCanvasTransform() {
    // Safety checks for canvas size
    var safeSize = widget.track.canvasSize;
    if (widget.track.canvasSize.width <= 0 ||
        widget.track.canvasSize.height <= 0 ||
        !widget.track.canvasSize.width.isFinite ||
        !widget.track.canvasSize.height.isFinite) {
      // Use canvas size as fallback
      safeSize = widget.canvasSize;
    }

    // Safety checks for scale
    var safeScale = widget.track.canvasScale;
    if (!safeScale.isFinite || safeScale <= 0) {
      safeScale = 1.0;
    }

    // Convert VideoTrackModel canvas properties to CanvasTransform
    return CanvasTransform(
      position: widget.track.canvasPosition,
      size: safeSize,
      scale: safeScale,
      rotation: widget.track.canvasRotation *
          (3.14159 / 180), // Convert degrees to radians
      cropRect: Rect.fromLTWH(
        widget.track.canvasCropRect.left,
        widget.track.canvasCropRect.top,
        widget.track.canvasCropRect.width,
        widget.track.canvasCropRect.height,
      ),
      opacity: widget.track.canvasOpacity,
    );
  }
}

/// Custom painter for rendering images with crop
class _ImagePainter extends CustomPainter {
  final ui.Image image;
  final Rect cropRect;

  _ImagePainter({
    required this.image,
    required this.cropRect,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate source rect from crop
    final sourceRect = Rect.fromLTWH(
      image.width * cropRect.left,
      image.height * cropRect.top,
      image.width * cropRect.width,
      image.height * cropRect.height,
    );

    // Destination rect is the full size
    final destRect = Rect.fromLTWH(0, 0, size.width, size.height);

    // Draw the cropped image
    canvas.drawImageRect(
      image,
      sourceRect,
      destRect,
      Paint()..filterQuality = FilterQuality.high,
    );
  }

  @override
  bool shouldRepaint(_ImagePainter oldDelegate) {
    return oldDelegate.image != image || oldDelegate.cropRect != cropRect;
  }
}
