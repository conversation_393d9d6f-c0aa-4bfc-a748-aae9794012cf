import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'video_canvas_item.dart';
import 'multi_video_canvas_painter.dart';
import 'individual_video_crop_dialog.dart';

/// Main container for multi-video canvas preview with gesture handling
class MultiVideoCanvasContainer extends StatefulWidget {
  final VideoEditorProvider provider;
  final Size containerSize;
  final GlobalKey canvasKey;
  final bool showGrid;
  final Color backgroundColor;
  final Function(int)? onVideoSelected;
  final Function(String, Offset)? onVideoMoved;
  final Function(String, Size)? onVideoResized;
  final Function(String, int)? onVideoRotated;

  const MultiVideoCanvasContainer({
    super.key,
    required this.provider,
    required this.containerSize,
    required this.canvasKey,
    this.showGrid = false,
    this.backgroundColor = Colors.black,
    this.onVideoSelected,
    this.onVideoMoved,
    this.onVideoResized,
    this.onVideoRotated,
  });

  @override
  State<MultiVideoCanvasContainer> createState() => _MultiVideoCanvasContainerState();
}

class _MultiVideoCanvasContainerState extends State<MultiVideoCanvasContainer>
    with TickerProviderStateMixin {
  
  // Canvas video items
  List<VideoCanvasItem> _canvasItems = [];
  
  // Selection and interaction state
  int _selectedVideoIndex = -1;
  String? _draggedVideoId;
  Offset? _dragStartPosition;
  Offset? _lastPanPosition;
  
  // Drag positions for smooth dragging
  final Map<String, Offset> _dragPositions = {};
  
  // Resize state
  ResizeHandle _activeResizeHandle = ResizeHandle.none;
  Size? _resizeStartSize;
  Offset? _resizeStartPosition;
  
  // Rotation state
  bool _isRotating = false;
  double _rotationStartAngle = 0;
  int _rotationStartValue = 0;
  
  // Animation controllers
  late AnimationController _selectionAnimController;
  late Animation<double> _selectionAnimation;
  
  // Performance optimization
  Timer? _dragUpdateTimer;
  DateTime? _lastUpdateTime;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller for selection
    _selectionAnimController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _selectionAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _selectionAnimController, curve: Curves.easeOut),
    );
    
    // Initialize canvas items from provider
    _initializeCanvasItems();
    
    // Listen to provider changes
    widget.provider.addListener(_onProviderChanged);
  }
  
  @override
  void dispose() {
    widget.provider.removeListener(_onProviderChanged);
    _selectionAnimController.dispose();
    _dragUpdateTimer?.cancel();
    super.dispose();
  }
  
  void _initializeCanvasItems() {
    _canvasItems.clear();
    
    print('🔨 INITIALIZING CANVAS ITEMS');
    print('Video tracks count: ${widget.provider.videoTracks.length}');
    
    for (int i = 0; i < widget.provider.videoTracks.length; i++) {
      final track = widget.provider.videoTracks[i];
      
      // Get individual video controller for this track
      final controller = widget.provider.getVideoControllerForTrack(track.id);
      
      print('Track $i (${track.id}):');
      print('  Original file: ${track.originalFile.path}');
      print('  Canvas position: ${track.canvasPosition}');
      print('  Canvas size: ${track.canvasSize}');
      print('  Controller initialized: ${controller?.video.value.isInitialized}');
      
      if (controller != null && controller.video.value.isInitialized) {
        print('  Video dimensions: ${controller.video.value.size}');
        
        // Use canvas properties from track instead of autoSized calculation
        final item = VideoCanvasItem(
          id: track.id,
          controller: controller,
          position: track.canvasPosition,
          size: track.canvasSize,
          scale: track.canvasScale,
          rotation: track.canvasRotation,
          zIndex: track.canvasZIndex,
          cropRect: track.canvasCropRect,
          isVisible: track.canvasVisible,
          opacity: track.canvasOpacity,
        );
        
        _canvasItems.add(item);
        print('  ✅ Added canvas item with position: ${item.position}');
      } else {
        print('  ❌ Controller not available or not initialized');
      }
    }
    
    print('Total canvas items created: ${_canvasItems.length}');
    print('');
    
    if (mounted) {
      setState(() {});
    }
  }
  
  void _onProviderChanged() {
    // Reinitialize canvas items when provider changes
    _initializeCanvasItems();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      key: widget.canvasKey,
      width: widget.containerSize.width,
      height: widget.containerSize.height,
      child: GestureDetector(
        onTapUp: _handleTapUp,
        onDoubleTap: _handleDoubleTap,
        onLongPress: _handleLongPress,
        onPanStart: _handlePanStart,
        onPanUpdate: _handlePanUpdate,
        onPanEnd: _handlePanEnd,
        child: Stack(
          children: [
            // Main canvas with multi-video painter
            CustomPaint(
              size: widget.containerSize,
              painter: MultiVideoCanvasPainter(
                videoItems: _canvasItems,
                globalTextTracks: widget.provider.textTracks,
                canvasSize: widget.containerSize,
                backgroundColor: widget.backgroundColor,
                currentTime: widget.provider.videoPosition,
                selectedVideoIndex: _selectedVideoIndex,
                showSelectionHandles: true,
                showGrid: widget.showGrid,
                dragPositions: _dragPositions,
              ),
            ),
            
            // Video players overlay (for actual video rendering)
            ..._buildVideoPlayerOverlays(),
            
            // Selection animation overlay
            if (_selectedVideoIndex >= 0)
              AnimatedBuilder(
                animation: _selectionAnimation,
                builder: (context, child) {
                  return _buildSelectionOverlay();
                },
              ),
          ],
        ),
      ),
    );
  }
  
  List<Widget> _buildVideoPlayerOverlays() {
    print('🔺 BUILDING VIDEO PLAYER OVERLAYS');
    print('Canvas items count: ${_canvasItems.length}');
    print('Container size: ${widget.containerSize.width} x ${widget.containerSize.height}');
    
    return _canvasItems.map((item) {
      final effectivePosition = _dragPositions[item.id] ?? item.position;
      
      print('📺 Video Item: ${item.id}');
      print('  Original video size: ${item.originalVideoSize.width} x ${item.originalVideoSize.height}');
      print('  Aspect ratio: ${item.aspectRatio}');
      print('  Canvas position: ${effectivePosition.dx}, ${effectivePosition.dy}');
      print('  Canvas size: ${item.size.width} x ${item.size.height}');
      print('  Scale: ${item.scale}');
      
      // Calculate the available space for the video
      final availableWidth = item.size.width * item.scale;
      final availableHeight = item.size.height * item.scale;
      
      print('  Available space: ${availableWidth.toStringAsFixed(1)} x ${availableHeight.toStringAsFixed(1)}');
      
      // Calculate actual video size maintaining aspect ratio
      double videoWidth, videoHeight;
      final aspectRatio = item.aspectRatio;
      
      if (availableWidth / availableHeight > aspectRatio) {
        // Available space is wider than video aspect ratio - fit to height
        videoHeight = availableHeight;
        videoWidth = videoHeight * aspectRatio;
        print('  Fitting to height (letterbox)');
      } else {
        // Available space is taller than video aspect ratio - fit to width
        videoWidth = availableWidth;
        videoHeight = videoWidth / aspectRatio;
        print('  Fitting to width (pillarbox)');
      }
      
      print('  Final video dimensions: ${videoWidth.toStringAsFixed(1)} x ${videoHeight.toStringAsFixed(1)}');
      
      // Center the video within the available space
      final videoOffsetX = (availableWidth - videoWidth) / 2;
      final videoOffsetY = (availableHeight - videoHeight) / 2;
      
      print('  Video offset: ${videoOffsetX.toStringAsFixed(1)}, ${videoOffsetY.toStringAsFixed(1)}');
      print('');
      
      return Positioned(
        left: effectivePosition.dx,
        top: effectivePosition.dy,
        width: availableWidth,
        height: availableHeight,
        child: Transform.rotate(
          angle: item.rotation * math.pi / 180,
          child: Container(
            width: availableWidth,
            height: availableHeight,
            color: Colors.transparent, // Show black canvas background through transparent areas
            child: Stack(
              children: [
                Positioned(
                  left: videoOffsetX,
                  top: videoOffsetY,
                  width: videoWidth,
                  height: videoHeight,
                  child: ClipRect(
                    clipper: item.cropRect != const Rect.fromLTWH(0, 0, 1, 1) 
                        ? VideoClipPath(item.cropRect) 
                        : null,
                    child: Opacity(
                      opacity: item.opacity,
                      child: VideoPlayer(item.controller.video),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }).toList();
  }
  
  Widget _buildSelectionOverlay() {
    if (_selectedVideoIndex < 0 || _selectedVideoIndex >= _canvasItems.length) {
      return const SizedBox.shrink();
    }
    
    final item = _canvasItems[_selectedVideoIndex];
    final effectivePosition = _dragPositions[item.id] ?? item.position;
    final opacity = _selectionAnimation.value;
    
    return Positioned(
      left: effectivePosition.dx - 2,
      top: effectivePosition.dy - 2,
      width: (item.size.width * item.scale) + 4,
      height: (item.size.height * item.scale) + 4,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.blue.withOpacity(opacity),
            width: 2.0,
          ),
        ),
        child: Stack(
          children: [
            // Corner resize handles
            ..._buildResizeHandles(item, opacity),
            
            // Rotation handle
            _buildRotationHandle(item, opacity),
          ],
        ),
      ),
    );
  }
  
  List<Widget> _buildResizeHandles(VideoCanvasItem item, double opacity) {
    const handleSize = 12.0;
    final width = (item.size.width * item.scale) + 4;
    final height = (item.size.height * item.scale) + 4;
    
    return [
      // Top-left
      Positioned(
        left: -handleSize / 2,
        top: -handleSize / 2,
        child: _buildResizeHandle(ResizeHandle.topLeft, opacity),
      ),
      // Top-right
      Positioned(
        right: -handleSize / 2,
        top: -handleSize / 2,
        child: _buildResizeHandle(ResizeHandle.topRight, opacity),
      ),
      // Bottom-left
      Positioned(
        left: -handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildResizeHandle(ResizeHandle.bottomLeft, opacity),
      ),
      // Bottom-right
      Positioned(
        right: -handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildResizeHandle(ResizeHandle.bottomRight, opacity),
      ),
    ];
  }
  
  Widget _buildResizeHandle(ResizeHandle handle, double opacity) {
    return GestureDetector(
      onPanStart: (details) => _startResize(handle, details),
      onPanUpdate: _updateResize,
      onPanEnd: _endResize,
      child: Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(opacity),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white.withOpacity(opacity)),
        ),
      ),
    );
  }
  
  Widget _buildRotationHandle(VideoCanvasItem item, double opacity) {
    const handleSize = 12.0;
    final width = (item.size.width * item.scale) + 4;
    
    return Positioned(
      left: width / 2 - handleSize / 2,
      top: -30,
      child: GestureDetector(
        onPanStart: _startRotation,
        onPanUpdate: _updateRotation,
        onPanEnd: _endRotation,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(opacity),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white.withOpacity(opacity)),
          ),
          child: Icon(
            Icons.rotate_right,
            size: 8,
            color: Colors.white.withOpacity(opacity),
          ),
        ),
      ),
    );
  }
  
  void _handleTapUp(TapUpDetails details) {
    final tapPosition = details.localPosition;
    
    // Find which video was tapped
    int tappedVideoIndex = -1;
    for (int i = _canvasItems.length - 1; i >= 0; i--) {
      final item = _canvasItems[i];
      if (item.containsPoint(tapPosition)) {
        tappedVideoIndex = i;
        break;
      }
    }
    
    // Update selection
    if (tappedVideoIndex != _selectedVideoIndex) {
      setState(() {
        _selectedVideoIndex = tappedVideoIndex;
      });
      
      if (tappedVideoIndex >= 0) {
        _selectionAnimController.forward();
        widget.onVideoSelected?.call(tappedVideoIndex);
        HapticFeedback.selectionClick();
      } else {
        _selectionAnimController.reverse();
      }
    }
  }

  void _handleDoubleTap() {
    if (_selectedVideoIndex >= 0) {
      _openCropDialog(_selectedVideoIndex);
    }
  }

  void _handleLongPress() {
    if (_selectedVideoIndex >= 0) {
      _showVideoContextMenu(_selectedVideoIndex);
    }
  }

  void _openCropDialog(int videoIndex) async {
    if (videoIndex < 0 || videoIndex >= _canvasItems.length) return;
    
    final item = _canvasItems[videoIndex];
    final track = widget.provider.videoTracks.firstWhere(
      (track) => track.id == item.id,
    );
    
    final result = await IndividualVideoCropHelper.showCropDialog(
      context,
      widget.provider,
      item,
      track.id,
    );
    
    if (result == true) {
      // Refresh the canvas after crop changes
      setState(() {});
      HapticFeedback.mediumImpact();
    }
  }

  void _showVideoContextMenu(int videoIndex) {
    if (videoIndex < 0 || videoIndex >= _canvasItems.length) return;
    
    final item = _canvasItems[videoIndex];
    final track = widget.provider.videoTracks.firstWhere(
      (track) => track.id == item.id,
    );
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Video Options',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildContextMenuItem(
              icon: Icons.crop,
              title: 'Crop Video',
              onTap: () {
                Navigator.pop(context);
                _openCropDialog(videoIndex);
              },
            ),
            _buildContextMenuItem(
              icon: Icons.rotate_right,
              title: 'Rotate 90°',
              onTap: () {
                Navigator.pop(context);
                _rotateVideo(videoIndex, 90);
              },
            ),
            _buildContextMenuItem(
              icon: Icons.flip,
              title: 'Reset Position',
              onTap: () {
                Navigator.pop(context);
                _resetVideoPosition(videoIndex);
              },
            ),
            _buildContextMenuItem(
              icon: item.isVisible ? Icons.visibility_off : Icons.visibility,
              title: item.isVisible ? 'Hide Video' : 'Show Video',
              onTap: () {
                Navigator.pop(context);
                _toggleVideoVisibility(videoIndex);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContextMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.white),
      title: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      onTap: onTap,
    );
  }

  void _rotateVideo(int videoIndex, int degrees) {
    if (videoIndex < 0 || videoIndex >= _canvasItems.length) return;
    
    final item = _canvasItems[videoIndex];
    item.rotate(degrees);
    widget.onVideoRotated?.call(item.id, item.rotation);
    setState(() {});
    HapticFeedback.lightImpact();
  }

  void _resetVideoPosition(int videoIndex) {
    if (videoIndex < 0 || videoIndex >= _canvasItems.length) return;
    
    final item = _canvasItems[videoIndex];
    // Calculate center position as default reset
    final newPosition = Offset(
      (widget.containerSize.width - item.size.width) / 2,
      (widget.containerSize.height - item.size.height) / 2,
    );
    
    item.updatePosition(newPosition, widget.containerSize);
    widget.onVideoMoved?.call(item.id, newPosition);
    setState(() {});
    HapticFeedback.mediumImpact();
  }

  void _toggleVideoVisibility(int videoIndex) {
    if (videoIndex < 0 || videoIndex >= _canvasItems.length) return;
    
    final item = _canvasItems[videoIndex];
    item.isVisible = !item.isVisible;
    
    widget.provider.updateVideoTrackCanvasProperties(
      item.id,
      visible: item.isVisible,
    );
    
    setState(() {});
    HapticFeedback.selectionClick();
  }
  
  void _handlePanStart(DragStartDetails details) {
    final startPosition = details.localPosition;
    
    // Find which video is being dragged
    for (int i = _canvasItems.length - 1; i >= 0; i--) {
      final item = _canvasItems[i];
      if (item.containsPoint(startPosition)) {
        _draggedVideoId = item.id;
        _dragStartPosition = startPosition;
        _lastPanPosition = item.position;
        
        // Select this video if not already selected
        if (_selectedVideoIndex != i) {
          setState(() {
            _selectedVideoIndex = i;
          });
          _selectionAnimController.forward();
          widget.onVideoSelected?.call(i);
        }
        
        HapticFeedback.selectionClick();
        break;
      }
    }
  }
  
  void _handlePanUpdate(DragUpdateDetails details) {
    if (_draggedVideoId == null || _dragStartPosition == null) return;
    
    // Throttle updates for performance
    final now = DateTime.now();
    if (_lastUpdateTime != null && 
        now.difference(_lastUpdateTime!).inMilliseconds < 16) {
      return;
    }
    _lastUpdateTime = now;
    
    final currentPosition = details.localPosition;
    final delta = currentPosition - _dragStartPosition!;
    final newPosition = _lastPanPosition! + delta;
    
    // Update drag position
    setState(() {
      _dragPositions[_draggedVideoId!] = newPosition;
    });
  }
  
  void _handlePanEnd(DragEndDetails details) {
    if (_draggedVideoId != null) {
      // Finalize the position
      final finalPosition = _dragPositions[_draggedVideoId!];
      if (finalPosition != null) {
        final itemIndex = _canvasItems.indexWhere((item) => item.id == _draggedVideoId);
        if (itemIndex >= 0) {
          final item = _canvasItems[itemIndex];
          item.updatePosition(finalPosition, widget.containerSize);
          
          // Clear drag position
          _dragPositions.remove(_draggedVideoId!);
          
          widget.onVideoMoved?.call(_draggedVideoId!, finalPosition);
        }
      }
      
      _draggedVideoId = null;
      _dragStartPosition = null;
      _lastPanPosition = null;
      
      setState(() {});
    }
  }
  
  void _startResize(ResizeHandle handle, DragStartDetails details) {
    if (_selectedVideoIndex >= 0) {
      final item = _canvasItems[_selectedVideoIndex];
      _activeResizeHandle = handle;
      _resizeStartSize = item.size;
      _resizeStartPosition = item.position;
      
      HapticFeedback.selectionClick();
    }
  }
  
  void _updateResize(DragUpdateDetails details) {
    if (_activeResizeHandle == ResizeHandle.none || 
        _selectedVideoIndex < 0 || 
        _resizeStartSize == null) return;
    
    final item = _canvasItems[_selectedVideoIndex];
    final delta = details.delta;
    
    // Calculate new size based on handle
    Size newSize = _resizeStartSize!;
    Offset newPosition = _resizeStartPosition!;
    
    switch (_activeResizeHandle) {
      case ResizeHandle.bottomRight:
        newSize = Size(
          (_resizeStartSize!.width + delta.dx).clamp(50.0, widget.containerSize.width),
          (_resizeStartSize!.height + delta.dy).clamp(50.0, widget.containerSize.height),
        );
        break;
      case ResizeHandle.topLeft:
        newSize = Size(
          (_resizeStartSize!.width - delta.dx).clamp(50.0, widget.containerSize.width),
          (_resizeStartSize!.height - delta.dy).clamp(50.0, widget.containerSize.height),
        );
        newPosition = Offset(
          _resizeStartPosition!.dx + delta.dx,
          _resizeStartPosition!.dy + delta.dy,
        );
        break;
      case ResizeHandle.topRight:
        newSize = Size(
          (_resizeStartSize!.width + delta.dx).clamp(50.0, widget.containerSize.width),
          (_resizeStartSize!.height - delta.dy).clamp(50.0, widget.containerSize.height),
        );
        newPosition = Offset(
          _resizeStartPosition!.dx,
          _resizeStartPosition!.dy + delta.dy,
        );
        break;
      case ResizeHandle.bottomLeft:
        newSize = Size(
          (_resizeStartSize!.width - delta.dx).clamp(50.0, widget.containerSize.width),
          (_resizeStartSize!.height + delta.dy).clamp(50.0, widget.containerSize.height),
        );
        newPosition = Offset(
          _resizeStartPosition!.dx + delta.dx,
          _resizeStartPosition!.dy,
        );
        break;
      default:
        break;
    }
    
    // Update item
    item.updateSize(newSize, maintainAspectRatio: true);
    item.updatePosition(newPosition, widget.containerSize);
    
    setState(() {});
  }
  
  void _endResize(DragEndDetails details) {
    if (_activeResizeHandle != ResizeHandle.none && _selectedVideoIndex >= 0) {
      final item = _canvasItems[_selectedVideoIndex];
      widget.onVideoResized?.call(item.id, item.size);
    }
    
    _activeResizeHandle = ResizeHandle.none;
    _resizeStartSize = null;
    _resizeStartPosition = null;
  }
  
  void _startRotation(DragStartDetails details) {
    if (_selectedVideoIndex >= 0) {
      final item = _canvasItems[_selectedVideoIndex];
      final center = item.renderRect.center;
      final startVector = details.localPosition - center;
      _rotationStartAngle = math.atan2(startVector.dy, startVector.dx);
      _rotationStartValue = item.rotation;
      _isRotating = true;
      
      HapticFeedback.selectionClick();
    }
  }
  
  void _updateRotation(DragUpdateDetails details) {
    if (!_isRotating || _selectedVideoIndex < 0) return;
    
    final item = _canvasItems[_selectedVideoIndex];
    final center = item.renderRect.center;
    final currentVector = details.localPosition - center;
    final currentAngle = math.atan2(currentVector.dy, currentVector.dx);
    
    final deltaAngle = currentAngle - _rotationStartAngle;
    final newRotation = (_rotationStartValue + (deltaAngle * 180 / math.pi)).round();
    
    // Snap to 90-degree increments
    final snappedRotation = ((newRotation / 90).round() * 90) % 360;
    
    if (snappedRotation != item.rotation) {
      item.rotation = snappedRotation;
      setState(() {});
      
      HapticFeedback.selectionClick();
    }
  }
  
  void _endRotation(DragEndDetails details) {
    if (_isRotating && _selectedVideoIndex >= 0) {
      final item = _canvasItems[_selectedVideoIndex];
      widget.onVideoRotated?.call(item.id, item.rotation);
    }
    
    _isRotating = false;
  }
}

/// Custom clipper for video crop functionality
class VideoClipPath extends CustomClipper<Rect> {
  final Rect cropRect;
  
  VideoClipPath(this.cropRect);
  
  @override
  Rect getClip(Size size) {
    return Rect.fromLTWH(
      cropRect.left * size.width,
      cropRect.top * size.height,
      cropRect.width * size.width,
      cropRect.height * size.height,
    );
  }
  
  @override
  bool shouldReclip(VideoClipPath oldClipper) {
    return oldClipper.cropRect != cropRect;
  }
}