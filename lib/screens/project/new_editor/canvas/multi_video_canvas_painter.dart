import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'video_canvas_item.dart';
import 'canvas_text_overlay_painter.dart';

/// Custom painter for rendering multiple videos on a canvas background
class MultiVideoCanvasPainter extends CustomPainter {
  final List<VideoCanvasItem> videoItems;
  final List<TextTrackModel> globalTextTracks;
  final Size canvasSize;
  final Color backgroundColor;
  final double currentTime;
  final int? selectedVideoIndex;
  final bool showSelectionHandles;
  final bool showGrid;

  // Drag state for video positioning
  final Map<String, Offset> dragPositions;

  MultiVideoCanvasPainter({
    required this.videoItems,
    required this.globalTextTracks,
    required this.canvasSize,
    this.backgroundColor = Colors.black,
    required this.currentTime,
    this.selectedVideoIndex,
    this.showSelectionHandles = true,
    this.showGrid = false,
    Map<String, Offset>? dragPositions,
  }) : dragPositions = dragPositions ?? {};

  @override
  void paint(Canvas canvas, Size size) {
    // Draw black canvas background
    _drawCanvasBackground(canvas, size);

    // Draw grid if enabled
    if (showGrid) {
      _drawGrid(canvas, size);
    }

    // Sort video items by z-index for proper layering
    final sortedItems = List<VideoCanvasItem>.from(videoItems)
      ..sort((a, b) => a.zIndex.compareTo(b.zIndex));

    // Draw each video item
    for (int i = 0; i < sortedItems.length; i++) {
      final item = sortedItems[i];
      if (item.isVisible) {
        _drawVideoItem(canvas, item, size);
        
        // Draw per-video text overlays
        _drawVideoTextOverlays(canvas, item, size);
        
        // Draw selection handles if this video is selected
        final originalIndex = videoItems.indexOf(item);
        if (showSelectionHandles && selectedVideoIndex == originalIndex) {
          _drawSelectionHandles(canvas, item);
        }
      }
    }

    // Draw global text overlays on top of all videos
    _drawGlobalTextOverlays(canvas, size);
  }

  void _drawCanvasBackground(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
  }

  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.2)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    const gridSpacing = 50.0;
    
    // Draw vertical lines
    for (double x = 0; x <= size.width; x += gridSpacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
    
    // Draw horizontal lines
    for (double y = 0; y <= size.height; y += gridSpacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  void _drawVideoItem(Canvas canvas, VideoCanvasItem item, Size size) {
    final effectivePosition = dragPositions[item.id] ?? item.position;
    
    canvas.save();
    
    // Apply transformations
    final center = Offset(
      effectivePosition.dx + (item.size.width * item.scale) / 2,
      effectivePosition.dy + (item.size.height * item.scale) / 2,
    );
    
    // Translate to center for rotation
    canvas.translate(center.dx, center.dy);
    
    // Apply rotation
    if (item.rotation != 0) {
      canvas.rotate(item.rotation * math.pi / 180);
    }
    
    // Apply scale
    canvas.scale(item.scale, item.scale);
    
    // Translate back
    canvas.translate(
      -item.size.width / 2,
      -item.size.height / 2,
    );

    // Draw video rectangle with crop
    _drawVideoRectangle(canvas, item);
    
    canvas.restore();
  }

  void _drawVideoRectangle(Canvas canvas, VideoCanvasItem item) {
    // Create paint for the video rectangle
    final paint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.8) // Placeholder color
      ..style = PaintingStyle.fill;

    // Draw cropped video area
    final videoRect = Rect.fromLTWH(0, 0, item.size.width, item.size.height);
    
    // Apply crop if specified
    if (item.cropRect != const Rect.fromLTWH(0, 0, 1, 1)) {
      final cropRect = Rect.fromLTWH(
        videoRect.left + (item.cropRect.left * videoRect.width),
        videoRect.top + (item.cropRect.top * videoRect.height),
        item.cropRect.width * videoRect.width,
        item.cropRect.height * videoRect.height,
      );
      
      canvas.clipRect(cropRect);
    }

    // Draw video placeholder (in real implementation, this would be the video frame)
    canvas.drawRect(videoRect, paint);
    
    // Draw video info overlay for debugging
    if (showGrid) {
      _drawVideoInfo(canvas, item, videoRect);
    }
  }

  void _drawVideoInfo(Canvas canvas, VideoCanvasItem item, Rect videoRect) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'Video ${item.id.substring(0, 8)}\n${item.size.width.toInt()}x${item.size.height.toInt()}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        videoRect.left + 8,
        videoRect.top + 8,
      ),
    );
  }

  void _drawVideoTextOverlays(Canvas canvas, VideoCanvasItem item, Size canvasSize) {
    if (item.textTracks.isEmpty) return;
    
    // Calculate video transformation for per-video text overlays
    final videoPosition = dragPositions[item.id] ?? item.position;
    
    // Create canvas text overlay painter for per-video text overlays
    final textPainter = CanvasTextOverlayPainter(
      textTracks: item.textTracks,
      currentTime: currentTime,
      containerSize: Size(item.size.width * item.scale, item.size.height * item.scale),
      videoSize: Size(item.originalVideoSize.width, item.originalVideoSize.height),
      isGlobal: false, // Per-video text
      selectedTextTrackIndex: -1, // No selection handling in painter mode
    );
    
    // Apply video transformations before rendering text
    canvas.save();
    canvas.translate(videoPosition.dx, videoPosition.dy);
    canvas.scale(item.scale);
    canvas.rotate(item.rotation * math.pi / 180);
    
    // Render text overlays relative to this video
    textPainter.paint(canvas, Size(item.size.width, item.size.height));
    
    canvas.restore();
  }

  void _drawGlobalTextOverlays(Canvas canvas, Size size) {
    if (globalTextTracks.isEmpty) return;
    
    // Filter visible text tracks
    final visibleTextTracks = globalTextTracks.where((track) => _isTrackVisible(track, currentTime)).toList();
    if (visibleTextTracks.isEmpty) return;
    
    // Calculate canvas video dimensions for global text positioning
    Size canvasVideoSize = size;
    
    // If we have videos on the canvas, use the primary video dimensions for coordinate mapping
    if (videoItems.isNotEmpty) {
      final primaryVideo = videoItems.first;
      canvasVideoSize = Size(primaryVideo.originalVideoSize.width, primaryVideo.originalVideoSize.height);
    }
    
    // Create canvas text overlay painter for global text overlays
    final textPainter = CanvasTextOverlayPainter(
      textTracks: visibleTextTracks,
      currentTime: currentTime,
      containerSize: size,
      videoSize: canvasVideoSize,
      isGlobal: true, // Global text overlays
      selectedTextTrackIndex: -1, // No selection handling in painter mode
    );
    
    // Render global text overlays directly on canvas
    textPainter.paint(canvas, size);
  }


  void _drawSelectionHandles(Canvas canvas, VideoCanvasItem item) {
    final effectivePosition = dragPositions[item.id] ?? item.position;
    final rect = Rect.fromLTWH(
      effectivePosition.dx,
      effectivePosition.dy,
      item.size.width * item.scale,
      item.size.height * item.scale,
    );

    // Draw selection border
    final borderPaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    canvas.drawRect(rect, borderPaint);

    // Draw corner handles
    final handlePaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;

    const handleSize = 8.0;
    final handles = [
      rect.topLeft,
      rect.topRight,
      rect.bottomLeft,
      rect.bottomRight,
    ];

    for (final handle in handles) {
      canvas.drawCircle(handle, handleSize / 2, handlePaint);
    }

    // Draw rotation handle
    final rotationHandlePosition = Offset(rect.center.dx, rect.top - 20);
    final rotationHandlePaint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(rotationHandlePosition, handleSize / 2, rotationHandlePaint);
    
    // Draw line connecting rotation handle
    final linePaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 1.0;
    
    canvas.drawLine(
      Offset(rect.center.dx, rect.top),
      rotationHandlePosition,
      linePaint,
    );
  }

  bool _isTrackVisible(TextTrackModel track, double currentTime) {
    return currentTime >= track.trimStartTime && 
           currentTime <= track.trimEndTime;
  }

  @override
  bool shouldRepaint(MultiVideoCanvasPainter oldDelegate) {
    return oldDelegate.videoItems != videoItems ||
           oldDelegate.globalTextTracks != globalTextTracks ||
           oldDelegate.currentTime != currentTime ||
           oldDelegate.selectedVideoIndex != selectedVideoIndex ||
           oldDelegate.dragPositions != dragPositions ||
           oldDelegate.showSelectionHandles != showSelectionHandles ||
           oldDelegate.showGrid != showGrid;
  }
}

/// Specialized painter for rendering actual video frames
class VideoFramePainter extends CustomPainter {
  final VideoPlayerController controller;
  final VideoCanvasItem item;
  final Offset? dragPosition;

  VideoFramePainter({
    required this.controller,
    required this.item,
    this.dragPosition,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (!controller.value.isInitialized) return;

    final effectivePosition = dragPosition ?? item.position;
    
    canvas.save();
    
    // Set up clipping for the video area
    final videoRect = Rect.fromLTWH(
      effectivePosition.dx,
      effectivePosition.dy,
      item.size.width * item.scale,
      item.size.height * item.scale,
    );
    
    canvas.clipRect(videoRect);
    
    // Apply transformations for rotation
    if (item.rotation != 0) {
      final center = videoRect.center;
      canvas.translate(center.dx, center.dy);
      canvas.rotate(item.rotation * math.pi / 180);
      canvas.translate(-center.dx, -center.dy);
    }

    // Draw video frame (this is a placeholder - actual implementation would use the video texture)
    final paint = Paint()
      ..color = Colors.grey[800]!
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(videoRect, paint);
    
    canvas.restore();
  }

  @override
  bool shouldRepaint(VideoFramePainter oldDelegate) {
    return oldDelegate.controller != controller ||
           oldDelegate.item != item ||
           oldDelegate.dragPosition != dragPosition;
  }
}