import 'package:provider/provider.dart';
import 'package:ai_video_creator_editor/components/crop/crop_grid.dart';
import 'package:ai_video_creator_editor/controllers/video_controller.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';

import 'package:flutter/material.dart';
import 'package:fraction/fraction.dart';

class CropPage extends StatelessWidget {
  const CropPage({super.key, required this.controller});

  final VideoEditorController controller;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(bottom: 10),
          child: Column(
            children: [
              Expanded(
                child: CropGridViewer.edit(
                  controller: controller,
                  rotateCropArea: false,
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                ),
              ),
              const SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    flex: 2,
                    child: IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Center(
                        child: Text(
                          "Cancel",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 6,
                    child: AnimatedBuilder(
                      animation: controller,
                      builder: (_, __) => Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                onPressed: () =>
                                    controller.preferredCropAspectRatio =
                                        controller.preferredCropAspectRatio
                                            ?.toFraction()
                                            .inverse()
                                            .toDouble(),
                                icon: controller.preferredCropAspectRatio !=
                                            null &&
                                        controller.preferredCropAspectRatio! < 1
                                    ? const Icon(
                                        Icons.panorama_vertical_select_rounded)
                                    : const Icon(
                                        Icons.panorama_vertical_rounded),
                              ),
                              IconButton(
                                onPressed: () =>
                                    controller.preferredCropAspectRatio =
                                        controller.preferredCropAspectRatio
                                            ?.toFraction()
                                            .inverse()
                                            .toDouble(),
                                icon: controller.preferredCropAspectRatio !=
                                            null &&
                                        controller.preferredCropAspectRatio! > 1
                                    ? const Icon(Icons
                                        .panorama_horizontal_select_rounded)
                                    : const Icon(
                                        Icons.panorama_horizontal_rounded),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              _buildCropButton(context, null),
                              _buildCropButton(context, 1.toFraction()),
                              _buildCropButton(
                                  context, Fraction.fromString("9/16")),
                              _buildCropButton(
                                  context, Fraction.fromString("3/4")),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: IconButton(
                      onPressed: () {
                        controller.applyCacheCrop();
                        final cropRect = controller.cropRect;
                        print(
                            'CropPage cropRect: left=${cropRect.left}, top=${cropRect.top}, width=${cropRect.width}, height=${cropRect.height}');
                        Provider.of<VideoEditorProvider>(context, listen: false)
                            .updateCropRect(cropRect);
                        Navigator.pop(context);
                      },
                      icon: Center(
                        child: Text(
                          "Done",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCropButton(BuildContext context, Fraction? f) {
    if (controller.preferredCropAspectRatio != null &&
        controller.preferredCropAspectRatio! > 1) {
      f = f?.inverse();
    }

    return Flexible(
      child: TextButton(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: controller.preferredCropAspectRatio == f?.toDouble()
              ? Colors.grey.shade800
              : null,
          foregroundColor: controller.preferredCropAspectRatio == f?.toDouble()
              ? Colors.white
              : null,
          textStyle: Theme.of(context).textTheme.bodySmall,
        ),
        onPressed: () => controller.preferredCropAspectRatio = f?.toDouble(),
        child: Text(f == null ? 'free' : '${f.numerator}:${f.denominator}'),
      ),
    );
  }
}
