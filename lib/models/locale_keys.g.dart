// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class LocaleKeys {
  static const images = 'images';
  static const videos = 'videos';
  static const audio = 'audio';
  static const file = 'file';
  static const download = 'download';
  static const oopsLostInSpace = 'oopsLostInSpace';
  static const uploadImage = 'uploadImage';
  static const videoLength = 'videoLength';
  static const videoLength14 = 'videoLength14';
  static const videoLength25 = 'videoLength25';
  static const videoSizingStrategy = 'videoSizingStrategy';
  static const videoSizingStrategyAspectRatio =
      'videoSizingStrategyAspectRatio';
  static const videoSizingStrategyCrop169 = 'videoSizingStrategyCrop169';
  static const videoSizingStrategyImageDimensions =
      'videoSizingStrategyImageDimensions';
  static const anError = 'anError';
  static const empty = 'empty';
  static const generateVideoEM = 'generateVideoEM';
  static const pleaseSelectImage = 'pleaseSelectImage';
  static const uploadVideo = 'uploadVideo';
  static const resolution = 'resolution';
  static const pickStyle = 'pickStyle';
  static const pickUpscale = 'pickUpscale';
  static const photoRealisticX4Plus = 'photoRealisticX4Plus';
  static const animationX4Plus = 'animationX4Plus';
  static const realisticAnimation = 'realisticAnimation';
  static const videoUpscale = 'videoUpscale';
  static const pleaseSelectVideo = 'pleaseSelectVideo';
  static const fps = 'fps';
  static const horizontalResolution = 'horizontalResolution';
  static const uploadAudioClip = 'uploadAudioClip';
  static const audioClip = 'audioClip';
  static const subjectImage = 'subjectImage';
  static const pickSubjectImage = 'pickSubjectImage';
  static const preProcess = 'preProcess';
  static const crop = 'crop';
  static const resize = 'resize';
  static const full = 'full';
  static const faceRenderer = 'faceRenderer';
  static const defaultT = 'defaultT';
  static const advanced = 'advanced';
  static const faceModelResolution = 'faceModelResolution';
  static const poseStyle = 'poseStyle';
  static const expressionScale = 'expressionScale';
  static const enhanceFace = 'enhanceFace';
  static const isStill = 'isStill';
  static const eyeBlink = 'eyeBlink';
  static const nickname = 'nickname';
  static const update = 'update';
  static const promptColon = 'promptColon';
  static const seedArgs = 'seedArgs';
  static const modelArgs = 'modelArgs';
  static const toExtendMusicUploadReferenceMusic =
      'toExtendMusicUploadReferenceMusic';
  static const promptMaker = 'promptMaker';
  static const textToMusic = 'textToMusic';
  static const musicRemixer = 'musicRemixer';
  static const instrument = 'instrument';
  static const piano = 'piano';
  static const violin = 'violin';
  static const acousticGuitar = 'acousticGuitar';
  static const electricGuitar = 'electricGuitar';
  static const drums = 'drums';
  static const bassGuitar = 'bassGuitar';
  static const synthesizer = 'synthesizer';
  static const saxophone = 'saxophone';
  static const flute = 'flute';
  static const clarinet = 'clarinet';
  static const trumpet = 'trumpet';
  static const cello = 'cello';
  static const viola = 'viola';
  static const trombone = 'trombone';
  static const harp = 'harp';
  static const accordion = 'accordion';
  static const oboe = 'oboe';
  static const frenchHorn = 'frenchHorn';
  static const ukulele = 'ukulele';
  static const harmonica = 'harmonica';
  static const mandolin = 'mandolin';
  static const banjo = 'banjo';
  static const doubleBass = 'doubleBass';
  static const bongoDrums = 'bongoDrums';
  static const congaDrums = 'congaDrums';
  static const timpani = 'timpani';
  static const xylophone = 'xylophone';
  static const marimba = 'marimba';
  static const tambourine = 'tambourine';
  static const djembe = 'djembe';
  static const search = 'search';
  static const uploadAudio = 'uploadAudio';
  static const music = 'music';
  static const uploadMusic = 'uploadMusic';
  static const toUploadAudioStorage = 'toUploadAudioStorage';
  static const toUploadAudioMusicNAudio = 'toUploadAudioMusicNAudio';
  static const pop = 'pop';
  static const rock = 'rock';
  static const classical = 'classical';
  static const jazz = 'jazz';
  static const blues = 'blues';
  static const hipHop = 'hipHop';
  static const electronicEdm = 'electronicEdm';
  static const country = 'country';
  static const reggae = 'reggae';
  static const folk = 'folk';
  static const rnbRhythmAndBlues = 'rnbRhythmAndBlues';
  static const loFi = 'loFi';
  static const metal = 'metal';
  static const soul = 'soul';
  static const funk = 'funk';
  static const gospel = 'gospel';
  static const disco = 'disco';
  static const house = 'house';
  static const techno = 'techno';
  static const trance = 'trance';
  static const dubstep = 'dubstep';
  static const drumAndBass = 'drumAndBass';
  static const ambient = 'ambient';
  static const indie = 'indie';
  static const grunge = 'grunge';
  static const punk = 'punk';
  static const ska = 'ska';
  static const worldMusic = 'worldMusic';
  static const newAge = 'newAge';
  static const latin = 'latin';
  static const progressiveRock = 'progressiveRock';
  static const hardRock = 'hardRock';
  static const heavyMetal = 'heavyMetal';
  static const psychedelicRock = 'psychedelicRock';
  static const folkRock = 'folkRock';
  static const bluesRock = 'bluesRock';
  static const reggaeton = 'reggaeton';
  static const salsa = 'salsa';
  static const bossaNova = 'bossaNova';
  static const flamenco = 'flamenco';
  static const afrobeat = 'afrobeat';
  static const dancehall = 'dancehall';
  static const kPop = 'kPop';
  static const jPop = 'jPop';
  static const cPop = 'cPop';
  static const samba = 'samba';
  static const merengue = 'merengue';
  static const bachata = 'bachata';
  static const calypso = 'calypso';
  static const neoSoul = 'neoSoul';
  static const output = 'output';
  static const duration = 'duration';
  static const referenceMusic = 'referenceMusic';
  static const uploadReferenceMusic = 'uploadReferenceMusic';
  static const audioFileInfluenceGeneratedMusic =
      'audioFileInfluenceGeneratedMusic';
  static const continuation = 'continuation';
  static const ifContinuationEnabled = 'ifContinuationEnabled';
  static const startTimeAudioContinuation = 'startTimeAudioContinuation';
  static const continuationStart = 'continuationStart';
  static const endTimeAudioContinuation = 'endTimeAudioContinuation';
  static const continuationEnd = 'continuationEnd';
  static const increasesInfluenceOfInputsOnOutput =
      'increasesInfluenceOfInputsOnOutput';
  static const outputFormat = 'outputFormat';
  static const generateMusic = 'generateMusic';
  static const musicComposer = 'musicComposer';
  static const pickSong = 'pickSong';
  static const searchSong = 'searchSong';
  static const pickVoice = 'pickVoice';
  static const extendSong = 'extendSong';
  static const style = 'style';
  static const guidance = 'guidance';
  static const seed = 'seed';
  static const seedDesc = 'seedDesc';
  static const seedDescArgs = 'seedDescArgs';
  static const promptStar = 'promptStar';
  static const textToAudio = 'textToAudio';
  static const voiceToVoice = 'voiceToVoice';
  static const audioCover = 'audioCover';
  static const transcriptST = 'transcriptST';
  static const transcript = 'transcript';
  static const transcriptOfAudio = 'transcriptOfAudio';
  static const uploadSpeakersVoice = 'uploadSpeakersVoice';
  static const emotion = 'emotion';
  static const uploadTranscriptAudio = 'uploadTranscriptAudio';
  static const anAudioYouWantToUseItsTranscript =
      'anAudioYouWantToUseItsTranscript';
  static const uploadTargetAudio = 'uploadTargetAudio';
  static const outputSampleVoiceToSpeakTheTranscript =
      'outputSampleVoiceToSpeakTheTranscript';
  static const uploadInitialAudio = 'uploadInitialAudio';
  static const language = 'language';
  static const gender = 'gender';
  static const all = 'all';
  static const maleGender = 'maleGender';
  static const femaleGender = 'femaleGender';
  static const neutral = 'neutral';
  static const guidanceScale = 'guidanceScale';
  static const steps = 'steps';
  static const frames = 'frames';
  static const d3 = 'd3';
  static const realistic = 'realistic';
  static const illustrated = 'illustrated';
  static const animation = 'animation';
  static const generateEm = 'generateEm';
  static const generateCartoonify = 'generateCartoonify';
  static const generate = 'generate';
  static const generateS = 'generateS';
  static const generateImages = 'generateImages';
  static const generateImage = 'generateImage';
  static const templates = 'templates';
  static const imageHeight = 'imageHeight';
  static const imageWidth = 'imageWidth';
  static const samples = 'samples';
  static const negativePromptOptional = 'negativePromptOptional';
  static const negativePromptDesc = 'negativePromptDesc';
  static const negativePromptVideoDesc = 'negativePromptVideoDesc';
  static const keepOriginalAudio = 'keepOriginalAudio';
  static const targetVideo = 'targetVideo';
  static const sourceVideo = 'sourceVideo';
  static const uploadFaceImage = 'uploadFaceImage';
  static const enhancedArt = 'enhancedArt';
  static const cinematic = 'cinematic';
  static const comicBook = 'comicBook';
  static const craftClay = 'craftClay';
  static const digitalArt = 'digitalArt';
  static const analogFilm = 'analogFilm';
  static const fantasyArt = 'fantasyArt';
  static const isometric = 'isometric';
  static const lineArt = 'lineArt';
  static const lowPoly = 'lowPoly';
  static const cyberPunk = 'cyberPunk';
  static const neonPunk = 'neonPunk';
  static const origami = 'origami';
  static const photographic = 'photographic';
  static const pixelArt = 'pixelArt';
  static const texture = 'texture';
  static const watercolor = 'watercolor';
  static const model3d = 'model3d';
  static const exampleActual8KPortrait = 'exampleActual8KPortrait';
  static const textToVideoHintText = 'textToVideoHintText';
  static const styleImage = 'styleImage';
  static const uploadStyleImage = 'uploadStyleImage';
  static const uploadImages = 'uploadImages';
  static const selectNImages = 'selectNImages';
  static const videoHeight = 'videoHeight';
  static const videoWidth = 'videoWidth';
  static const promptStart = 'promptStart';
  static const promptEnd = 'promptEnd';
  static const orBig = 'orBig';
  static const forgotPasswordQ = 'forgotPasswordQ';
  static const forgotPass = 'forgotPass';
  static const password = 'password';
  static const selectImageRatio = 'selectImageRatio';
  static const selectVideoRatio = 'selectVideoRatio';
  static const imageRatio = 'imageRatio';
  static const square = 'square';
  static const landscape = 'landscape';
  static const socialMedia = 'socialMedia';
  static const portrait = 'portrait';
  static const standard = 'standard';
  static const copiedSuccessfully = 'copiedSuccessfully';
  static const somethingWentWrongPleaseTryLater =
      'somethingWentWrongPleaseTryLater';
  static const somethingWentWrongPleaseCheckYourNetwork =
      'somethingWentWrongPleaseCheckYourNetwork';
  static const processingDot = 'processingDot';
  static const more = 'more';
  static const less = 'less';
  static const none = 'none';
  static const photoRealistic = 'photoRealistic';
  static const pleaseSignInToProceed = 'pleaseSignInToProceed';
  static const invalidOTP = 'invalidOTP';
  static const lowBalance = 'lowBalance';
  static const pleaseTopUp = 'pleaseTopUp';
  static const delete = 'delete';
  static const areYouSure = 'areYouSure';
  static const close = 'close';
  static const deletedSuccessfully = 'deletedSuccessfully';
  static const error = 'error';
  static const signIn = 'signIn';
  static const pleaseEnterValidEmail = 'pleaseEnterValidEmail';
  static const fillAllFields = 'fillAllFields';
  static const signUp = 'signUp';
  static const pleaseFillRequiredFields = 'pleaseFillRequiredFields';
  static const bySigningUpAgreeToThe = 'bySigningUpAgreeToThe ';
  static const termsOfUse = 'termsOfUse';
  static const and = 'and';
  static const privacyPolicy = 'privacyPolicy';
  static const termsOfUseAndPrivacyPolicy = 'termsOfUseAndPrivacyPolicy';
  static const sendEmailVerification = 'sendEmailVerification';
  static const errorResettingPasswordEmailNotFound =
      'errorResettingPasswordEmailNotFound';
  static const inputValidEmail = 'inputValidEmail';
  static const emailOTP = 'emailOTP';
  static const newPasscode = 'newPasscode';
  static const pleaseFillAllFields = 'pleaseFillAllFields';
  static const resetPass = 'resetPass';
  static const continueWithApple = 'continueWithApple';
  static const continueWithGoogle = 'continueWithGoogle';
  static const continueWithEmail = 'continueWithEmail';
  static const verification = 'verification';
  static const checkEmail = 'checkEmail';
  static const cannotFindVerificationCode = 'cannotFindVerificationCode';
  static const completeSignUp = 'completeSignUp';
  static const verify = 'verify';
  static const custom = 'custom';
  static const textToVideo = 'textToVideo';
  static const pleaseEnterPrompt = 'pleaseEnterPrompt';
  static const pleaseEnterPromptImage = 'pleaseEnterPromptImage';
  static const properGuidanceValue = 'properGuidanceValue';
  static const keepImageSize = 'keepImageSize';
  static const imageToVideo = 'imageToVideo';
  static const copy = 'copy';
  static const strength = 'strength';
  static const imageIndex = 'imageIndex';
  static const morphMix = 'morphMix';
  static const uploadFaceVideo = 'uploadFaceVideo';
  static const faceVideo = 'faceVideo';
  static const faceImage = 'faceImage';
  static const taskType = 'taskType';
  static const videoToGif = 'videoToGif';
  static const extractVideoMp3 = 'extractVideoMp3';
  static const imagesToMp4 = 'imagesToMp4';
  static const imagesToGif = 'imagesToGif';
  static const videoToFrames = 'videoToFrames';
  static const reverseVideo = 'reverseVideo';
  static const bounceVideo = 'bounceVideo';
  static const uploadWillCostNStandardTokens = 'uploadWillCostNStandardTokens';
  static const saveAsTemplate = 'saveAsTemplate';
  static const generateVoiceEM = 'generateVoiceEM';
  static const target = 'target';
  static const logOut = 'logOut';
  static const aboutUs = 'aboutUs';
  static const aboutUsDesc1 = 'aboutUsDesc1';
  static const aboutUsDesc2 = 'aboutUsDesc2';
  static const aboutUsDesc3 = 'aboutUsDesc3';
  static const aboutUsDesc4 = 'aboutUsDesc4';
  static const aboutUsDesc5 = 'aboutUsDesc5';
  static const aboutUsDesc6 = 'aboutUsDesc6';
  static const email = 'email';
  static const deleteAccountTitle = 'deleteAccountTitle';
  static const feedback = 'feedback';
  static const letUsKnow = 'letUsKnow';
  static const submit = 'submit';
  static const feedbackSentSuccessfully = 'feedbackSentSuccessfully';
  static const feedbackIsRequired = 'feedbackIsRequired';
  static const langSelect = 'langSelect';
  static const pleaseTypeYourEmail = 'pleaseTypeYourEmail';
  static const pleaseTypePassword = 'pleaseTypePassword';
  static const yourPassAtLeast8 = 'yourPassAtLeast8';
  static const downloadSuccessfully = 'downloadSuccessfully';
  static const success = 'success';
  static const yourGeneratedVoiceSavedIOS = 'yourGeneratedVoiceSavedIOS';
  static const yourGeneratedFileSavedAndroid = 'yourGeneratedFileSavedAndroid';
  static const yourGeneratedVoiceSavedAndroid =
      'yourGeneratedVoiceSavedAndroid';
  static const yourGeneratedFileSavedIOS = 'yourGeneratedFileSavedIOS';
  static const yourMasterpieceIsProcessing = 'yourMasterpieceIsProcessing';
  static const notice = 'notice';
  static const notNow = 'notNow';
  static const settings = 'settings';
  static const continueT = 'continueT';
  static const selectAnImage = 'selectAnImage';
  static const selectAVideo = 'selectAVideo';
  static const or = 'or';
  static const fromURL = 'fromURL';
  static const pasteImageURL = 'pasteImageURL';
  static const paste = 'paste';
  static const uRLNotFromImage = 'uRLNotFromImage';
  static const add = 'add';
  static const pleaseTryAnotherImage = 'pleaseTryAnotherImage';
  static const areYouSureDeleteTemplate = 'areYouSureDeleteTemplate';
  static const selectCategory = 'selectCategory';
  static const pleaseGrantPermission = 'pleaseGrantPermission';
  static const pleaseAllowFullPhotosAccess = 'pleaseAllowFullPhotosAccess';
  static const toUploadPhotoIos = 'toUploadPhotoIos';
  static const toUploadCameraIos = 'toUploadCameraIos';
  static const toUploadCameraAndroid = 'toUploadCameraAndroid';
  static const toUploadPhotoAndroid = 'toUploadPhotoAndroid';
  static const toUploadPhotoAndroid33 = 'toUploadPhotoAndroid33';
  static const pleaseAllowCameraEditAvatar = 'pleaseAllowCameraEditAvatar';
  static const pleasePickShorterVideo = 'pleasePickShorterVideo';
  static const grantPermission = 'grantPermission';
  static const toUploadVideosIos = 'toUploadVideosIos';
  static const toUploadVideosAndroid = 'toUploadVideosAndroid';
  static const toUploadVideosAndroid33 = 'toUploadVideosAndroid33';
  static const pickShorterAudio = 'pickShorterAudio';
  static const pleaseAllowStorageAccess = 'pleaseAllowStorageAccess';
  static const pleaseAllowMusicAccess = 'pleaseAllowMusicAccess';
  static const toUploadAudioIos = 'toUploadAudioIos';
  static const toUploadAudioAndroid = 'toUploadAudioAndroid';
  static const toUploadAudioAndroid33 = 'toUploadAudioAndroid33';
  static const camera = 'camera';
  static const editImage = 'editImage';
  static const video = 'video';
  static const savedSpeech = 'savedSpeech';
  static const gallery = 'gallery';
  static const info = 'info';
  static const deleteDesc = 'deleteDesc';
  static const accountStatus = 'accountStatus';
  static const undoDelete = 'undoDelete';
  static const deleteAccount = 'deleteAccount';
  static const emailIsRequired = 'emailIsRequired';
  static const deleteAccountDesc = 'deleteAccountDesc';
  static const cancel = 'cancel';
  static const aiHug = 'aiHug';
  static const separatedImages = 'separatedImages';
  static const singleImage = 'singleImage';
  static const forBestResult = 'forBestResult';
  static const positionFacesClosely = 'positionFacesClosely';
  static const ensurePeopleFaceEachOther = 'ensurePeopleFaceEachOther';
  static const avoidExcessiveZoom = 'avoidExcessiveZoom';
  static const keepImageBgSimilar = 'keepImageBgSimilar';
  static const exampleHug = 'exampleHug';
  static const pleaseSelectFirstSecondPersonImage =
      'pleaseSelectFirstSecondPersonImage';
  static const pleaseUploadImageWithPeople = 'pleaseUploadImageWithPeople';
  static const hug = 'hug';
  static const createAHuggingVideo = 'createAHuggingVideo';
  static const kiss = 'kiss';
  static const createVideoTwoPeopleKissEachOther =
      'createVideoTwoPeopleKissEachOther';
  static const handshake = 'handshake';
  static const createVideoTwoPeopleShakingHands =
      'createVideoTwoPeopleShakingHands';
  static const dancing = 'dancing';
  static const createDancingVideo = 'createDancingVideo';
  static const customContentInTheVideo = 'customContentInTheVideo';
  static const anImageWithTwoPeople = 'anImageWithTwoPeople';
  static const tapToSelect = 'tapToSelect';
  static const firstPerson = 'firstPerson';
  static const secondPerson = 'secondPerson';
  static const caption = 'caption';
  static const scriptWriter = 'scriptWriter';
  static const videoToolkit = 'videoToolkit';
  static const livePortrait = 'livePortrait';
  static const imageRetalker = 'imageRetalker';
  static const videoRetalker = 'videoRetalker';
  static const videoExtend = 'videoExtend';
  static const cartoonifyVideo = 'cartoonifyVideo';
  static const voiceGeneration = 'voiceGeneration';
  static const musicGeneration = 'musicGeneration';
  static const uploadEndingImage = 'uploadEndingImage';
  static const endingImage = 'endingImage';
  static const detailedDescriptionVideoAstronaut =
      'detailedDescriptionVideoAstronaut';
  static const ratio = 'ratio';
  static const negative = 'negative';
  static const enhancePrompt = 'enhancePrompt';
  static const durationSeconds = 'durationSeconds';
  static const vertical = 'vertical';
  static const widescreen = 'widescreen';
  static const aiIsBusy = 'aiIsBusy';
  static const turbo = 'turbo';
  static const videoToVideo = 'videoToVideo';
  static const addSound = 'addSound';
  static const extendVideo = 'extendVideo';
  static const soundPromptStar = 'soundPromptStar';
  static const describeTheSoundForVideo = 'describeTheSoundForVideo';
  static const keepVideoSize = 'keepVideoSize';
}
