{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:3885931900534919121", "lastPropertyId": "4:865273178067906958", "name": "GeneratedAudioMeta", "properties": [{"id": "1:7536604391649525395", "name": "id", "type": 6, "flags": 1}, {"id": "2:4610291104066293756", "name": "prompt", "type": 9}, {"id": "3:372239420740692067", "name": "originalFilePath", "type": 9}, {"id": "4:865273178067906958", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": 9}], "relations": []}], "lastEntityId": "1:3885931900534919121", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}