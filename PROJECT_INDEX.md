# VideoCap Project Index

## Project Overview
**VideoCap** (ai_video_creator_editor) is a comprehensive Flutter-based mobile application for AI-powered video creation and editing. The app provides advanced video editing capabilities including caption generation, video GPT features, multi-track editing, and various video enhancement tools.

## Technology Stack
- **Framework**: Flutter 3.4.3+
- **Language**: Dart
- **Database**: ObjectBox (local NoSQL database)
- **Video Processing**: FFmpeg Kit Flutter
- **State Management**: Provider pattern
- **Localization**: Easy Localization (English, Chinese)
- **Video Player**: Video Player, Flick Video Player
- **Audio Processing**: AudioPlayers, Audio Waveforms

## Project Structure

### Core Directories
```
lib/
├── components/          # Reusable UI components
├── constants/          # App constants (colors, URLs, filters)
├── controllers/        # Business logic controllers
├── database/          # ObjectBox database models and singleton
├── enums/             # Enumeration definitions
├── models/            # Data models and structures
├── routes/            # Navigation routing
├── screens/           # UI screens and pages
├── utils/             # Utility functions and helpers
└── widgets/           # Custom widgets
```

### Key Features

#### 1. Video Editing (`lib/screens/project/`)
- **Multi-track video editing** with overlay support
- **Video trimming and cropping** functionality
- **Canvas-based positioning** system for video tracks
- **Rotation, scaling, and transformation** controls
- **Filter application** and visual effects
- **Transition effects** between video segments
- **Speed control** and playback manipulation
- **Volume control** for audio tracks

#### 2. Caption Generation (`lib/screens/tools/caption/`)
- **AI-powered caption generation** from video audio
- **Karaoke-style subtitle effects** with word-by-word highlighting
- **SRT to ASS conversion** for advanced subtitle formatting
- **Custom positioning** and styling for captions
- **Real-time preview** during caption editing

#### 3. Video GPT (`lib/screens/tools/vid_gpt/`)
- **AI-generated video content** based on text prompts
- **Multi-chapter video generation** with custom segments
- **Voice synthesis** integration with multiple voice options
- **Automated video compilation** from generated content
- **Custom orientation support** (portrait/landscape)

#### 4. Advanced Video Editor (`lib/screens/project/new_editor/`)
- **Timeline-based editing** interface
- **Multi-layer composition** with video, audio, and text tracks
- **Real-time preview** with playback controls
- **Export functionality** with quality options
- **Canvas-based layout** system for precise positioning

## Core Components

### Controllers
- **VideoEditorController**: Main video editing logic
- **CaptionsController**: Caption generation and management
- **VideoEditorProvider**: State management for video editing
- **AssetController**: Media asset management
- **AzureProvider**: Cloud service integration

### Models
- **VideoTrackModel**: Video track data structure with canvas properties
- **AudioTrackModel**: Audio track management
- **GeneratedAudioMeta**: Database model for generated audio metadata
- **VideoGptModel**: Data structure for AI-generated video content

### Database
- **ObjectBox**: Local NoSQL database for metadata storage
- **GeneratedAudioMeta**: Entity for tracking generated audio files
- **Singleton pattern** for database access

## Key Dependencies

### Video & Media Processing
- `video_player: ^2.9.2` - Video playback
- `video_editor: ^3.0.0` - Video editing capabilities
- `ffmpeg_kit_flutter_new: ^1.6.1` - Video processing
- `video_thumbnail: ^0.5.3` - Thumbnail generation
- `audioplayers: ^6.1.0` - Audio playback

### UI & State Management
- `provider: ^6.1.2` - State management
- `loader_overlay: ^5.0.0` - Loading overlays
- `easy_localization: ^3.0.7+1` - Internationalization
- `flutter_colorpicker: ^1.1.0` - Color selection

### File & Media Handling
- `file_picker: ^8.1.7` - File selection
- `image_picker: ^1.1.2` - Image/video capture
- `gal: ^2.3.1` - Gallery access
- `permission_handler: ^11.3.1` - Permissions management

### Database & Storage
- `objectbox: ^4.1.0` - Local database
- `shared_preferences: ^2.4.0` - Settings storage
- `path_provider: ^2.1.5` - File system paths

## Navigation Structure

### Routes (`lib/routes/route_names.dart`)
- `landing` - Main landing page
- `splash` - App initialization screen
- `caption` - Caption generation tool
- `projects` - Project management
- `videoGpt` - AI video generation
- `advancedVideoEditorPage` - Advanced editing interface

## Assets Structure
```
assets/
├── fonts/          # Custom fonts
├── gifs/           # Animated assets
├── icons/          # App icons
├── images/         # Static images
└── translations/   # Localization files
```

## Development Setup

### Prerequisites
- Flutter SDK 3.4.3+
- Dart SDK
- Android Studio / Xcode for mobile development
- FFmpeg for video processing

### Build Configuration
- **Android**: Configured in `android/` directory
- **iOS**: Configured in `ios/` directory
- **Package Name**: `ai_video_creator_editor`
- **Version**: 1.0.0+1

## Key Features Summary

1. **Multi-track Video Editing**: Professional-grade video editing with multiple video and audio tracks
2. **AI Caption Generation**: Automatic subtitle generation with advanced styling options
3. **Video GPT Integration**: AI-powered video content creation from text prompts
4. **Canvas-based Composition**: Precise positioning and layering of video elements
5. **Real-time Preview**: Live preview of edits during the editing process
6. **Export Functionality**: High-quality video export with customizable settings
7. **Localization Support**: Multi-language support (English, Chinese)
8. **Cloud Integration**: Azure services for enhanced AI capabilities

## Architecture Patterns
- **Provider Pattern**: For state management across the application
- **Singleton Pattern**: For database and preference management
- **Factory Pattern**: For creating video track models
- **Observer Pattern**: For real-time UI updates during video playback

This index provides a comprehensive overview of the VideoCap project structure, features, and technical implementation details.
